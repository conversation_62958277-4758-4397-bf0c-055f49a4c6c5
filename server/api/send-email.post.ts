import { Resend } from 'resend'

export default defineEventHandler(async (event) => {
  try {
    const config = useRuntimeConfig()
    const body = await readBody(event)
    const { type, ...emailData } = body

    if (!config.resendApiKey) {
      throw createError({
        statusCode: 500,
        statusMessage: 'RESEND_API_KEY is not configured'
      })
    }

    const resend = new Resend(config.resendApiKey)

    let emailContent = {}

    switch (type) {
      case 'beneficiary-notification':
        emailContent = createBeneficiaryNotificationEmail(emailData)
        break
      case 'trusted-contact-notification':
        emailContent = createTrustedContactNotificationEmail(emailData)
        break
      case 'trigger-alert':
        emailContent = createTriggerAlertEmail(emailData)
        break
      default:
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid email type'
        })
    }

    const { data, error } = await resend.emails.send(emailContent)

    if (error) {
      throw createError({
        statusCode: 500,
        statusMessage: error.message
      })
    }

    return {
      success: true,
      messageId: data?.id,
      message: 'Email sent successfully'
    }

  } catch (error: any) {
    console.error('Error sending email:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Failed to send email'
    })
  }
})

function createBeneficiaryNotificationEmail({ beneficiaryEmail, beneficiaryName, userEmail, userName }: any) {
  return {
    from: 'Volnt <<EMAIL>>',
    to: [beneficiaryEmail],
    subject: `You've been added as a beneficiary by ${userName}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>You've been added as a beneficiary</title>
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
              line-height: 1.6; 
              color: #333; 
              margin: 0; 
              padding: 0; 
            }
            .container { 
              max-width: 600px; 
              margin: 0 auto; 
              background: #ffffff; 
            }
            .header { 
              background: linear-gradient(135deg, #2563eb, #1d4ed8); 
              color: white; 
              padding: 30px 20px; 
              text-align: center; 
            }
            .header h1 { 
              margin: 0; 
              font-size: 24px; 
              font-weight: 600; 
            }
            .content { 
              padding: 30px 20px; 
              background: #ffffff; 
            }
            .content h2 { 
              color: #1f2937; 
              margin-top: 0; 
            }
            .highlight-box { 
              background: #f3f4f6; 
              border-left: 4px solid #2563eb; 
              padding: 20px; 
              margin: 20px 0; 
              border-radius: 0 8px 8px 0; 
            }
            .button { 
              display: inline-block; 
              background: #2563eb; 
              color: white; 
              padding: 12px 24px; 
              text-decoration: none; 
              border-radius: 8px; 
              margin: 20px 0; 
              font-weight: 500; 
            }
            .footer { 
              padding: 20px; 
              text-align: center; 
              color: #6b7280; 
              font-size: 14px; 
              background: #f9fafb; 
            }
            ul { 
              padding-left: 20px; 
            }
            li { 
              margin: 8px 0; 
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🛡️ Volnt - Digital Will & Testament</h1>
            </div>
            <div class="content">
              <h2>Hello ${beneficiaryName},</h2>
              
              <div class="highlight-box">
                <p><strong>${userName}</strong> has added you as a beneficiary in their digital will and testament on Volnt.</p>
              </div>
              
              <p>This is an important designation that means ${userName} has chosen to include you in their digital legacy planning.</p>
              
              <h3>What this means:</h3>
              <ul>
                <li>You are now listed as a beneficiary in their digital will</li>
                <li>You may receive access to specific digital or physical assets when the will is executed</li>
                <li>You will be notified of any important updates to your beneficiary status</li>
                <li>Your contact information will be kept secure and used only for will-related communications</li>
              </ul>
              
              <h3>Next Steps:</h3>
              <ul>
                <li><strong>Keep this email</strong> for your records</li>
                <li><strong>Ensure your contact information stays current</strong> - reach out to ${userName} if you change your email or phone number</li>
                <li><strong>Consider creating your own digital will</strong> to protect your own digital legacy</li>
              </ul>
              
              <p>If you have any questions about this designation or need to discuss your role as a beneficiary, please contact ${userName} directly at <a href="mailto:${userEmail}">${userEmail}</a>.</p>
              
              <div style="text-align: center;">
                <a href="https://volnt.xyz" class="button">Learn More About Volnt</a>
              </div>
            </div>
            <div class="footer">
              <p>This email was sent by Volnt on behalf of ${userName}.</p>
              <p><strong>Volnt</strong> - Secure your digital legacy</p>
              <p>If you believe you received this email in error, please contact ${userName} directly.</p>
            </div>
          </div>
        </body>
      </html>
    `
  }
}

function createTrustedContactNotificationEmail({ contactEmail, contactName, userEmail, userName }: any) {
  return {
    from: 'Volnt <<EMAIL>>',
    to: [contactEmail],
    subject: `You've been added as a trusted contact by ${userName}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>You've been added as a trusted contact</title>
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
              line-height: 1.6; 
              color: #333; 
              margin: 0; 
              padding: 0; 
            }
            .container { 
              max-width: 600px; 
              margin: 0 auto; 
              background: #ffffff; 
            }
            .header { 
              background: linear-gradient(135deg, #059669, #047857); 
              color: white; 
              padding: 30px 20px; 
              text-align: center; 
            }
            .header h1 { 
              margin: 0; 
              font-size: 24px; 
              font-weight: 600; 
            }
            .content { 
              padding: 30px 20px; 
              background: #ffffff; 
            }
            .content h2 { 
              color: #1f2937; 
              margin-top: 0; 
            }
            .warning-box { 
              background: #fef3c7; 
              border: 1px solid #f59e0b; 
              padding: 20px; 
              border-radius: 8px; 
              margin: 20px 0; 
            }
            .warning-box h3 { 
              color: #92400e; 
              margin-top: 0; 
            }
            .info-box { 
              background: #f0f9ff; 
              border-left: 4px solid #059669; 
              padding: 20px; 
              margin: 20px 0; 
              border-radius: 0 8px 8px 0; 
            }
            .button { 
              display: inline-block; 
              background: #059669; 
              color: white; 
              padding: 12px 24px; 
              text-decoration: none; 
              border-radius: 8px; 
              margin: 20px 0; 
              font-weight: 500; 
            }
            .footer { 
              padding: 20px; 
              text-align: center; 
              color: #6b7280; 
              font-size: 14px; 
              background: #f9fafb; 
            }
            ul { 
              padding-left: 20px; 
            }
            li { 
              margin: 8px 0; 
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🤝 Volnt - Trusted Contact</h1>
            </div>
            <div class="content">
              <h2>Hello ${contactName},</h2>
              
              <div class="info-box">
                <p><strong>${userName}</strong> has added you as a trusted contact for their digital will and testament on Volnt.</p>
              </div>
              
              <div class="warning-box">
                <h3>⚠️ Important Responsibility</h3>
                <p>As a trusted contact, you may be contacted to verify ${userName}'s status if they become inactive for an extended period. This is an important role in protecting their digital legacy.</p>
              </div>
              
              <h3>Your Role as a Trusted Contact:</h3>
              <ul>
                <li><strong>Verification requests:</strong> You may receive emails asking you to verify ${userName}'s status if they become unresponsive</li>
                <li><strong>Status confirmation:</strong> You'll help confirm their wellbeing during the will execution process</li>
                <li><strong>False trigger protection:</strong> Your response helps protect against accidental will execution</li>
                <li><strong>Emergency contact:</strong> You serve as a reliable point of contact for ${userName}</li>
              </ul>
              
              <h3>What to Expect:</h3>
              <ul>
                <li>Periodic check-in emails (only when ${userName} has been inactive)</li>
                <li>Clear verification requests with simple response instructions</li>
                <li>No access to ${userName}'s personal information or assets</li>
                <li>Respectful and secure communication from Volnt</li>
              </ul>
              
              <h3>How to Respond:</h3>
              <p>If you receive a verification request:</p>
              <ul>
                <li>Try to contact ${userName} using your usual communication methods</li>
                <li>Respond to the verification email with your findings</li>
                <li>Be honest about whether you can or cannot reach them</li>
              </ul>
              
              <p>If you have questions about this role, need to update your contact information, or want to be removed as a trusted contact, please reach out to ${userName} directly at <a href="mailto:${userEmail}">${userEmail}</a>.</p>
              
              <div style="text-align: center;">
                <a href="https://volnt.xyz" class="button">Learn More About Volnt</a>
              </div>
            </div>
            <div class="footer">
              <p>This email was sent by Volnt on behalf of ${userName}.</p>
              <p><strong>Volnt</strong> - Secure your digital legacy</p>
              <p>If you believe you received this email in error, please contact ${userName} directly.</p>
            </div>
          </div>
        </body>
      </html>
    `
  }
}

function createTriggerAlertEmail({ recipients, alertType, userName, userEmail, daysInactive, customMessage }: any) {
  let subject = ''
  let htmlContent = ''

  if (alertType === 'inactivity_warning') {
    subject = `⚠️ Inactivity Alert: ${userName} - ${daysInactive || 'Unknown'} days`
    htmlContent = generateInactivityWarningEmail(userName, userEmail, daysInactive, customMessage)
  } else if (alertType === 'verification_request') {
    subject = `🔍 Verification Required: ${userName}'s Digital Will`
    htmlContent = generateVerificationRequestEmail(userName, userEmail, customMessage)
  }

  return {
    from: 'Volnt Alerts <<EMAIL>>',
    to: recipients.map((r: any) => r.email),
    subject: subject,
    html: htmlContent
  }
}

function generateInactivityWarningEmail(userName: string, userEmail: string, daysInactive: number, customMessage?: string): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Inactivity Alert</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
          }
          .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: #ffffff; 
          }
          .header { 
            background: linear-gradient(135deg, #dc2626, #b91c1c); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
          }
          .header h1 { 
            margin: 0; 
            font-size: 24px; 
            font-weight: 600; 
          }
          .content { 
            padding: 30px 20px; 
            background: #ffffff; 
          }
          .alert-box { 
            background: #fef2f2; 
            border: 1px solid #dc2626; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
          }
          .alert-box h2 { 
            color: #dc2626; 
            margin-top: 0; 
          }
          .custom-message { 
            background: #f3f4f6; 
            border-left: 4px solid #6b7280; 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 0 8px 8px 0; 
          }
          .footer { 
            padding: 20px; 
            text-align: center; 
            color: #6b7280; 
            font-size: 14px; 
            background: #f9fafb; 
          }
          ul { 
            padding-left: 20px; 
          }
          li { 
            margin: 8px 0; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 Volnt Inactivity Alert</h1>
          </div>
          <div class="content">
            <h2>Hello,</h2>
            
            <div class="alert-box">
              <h2>⚠️ Inactivity Detected</h2>
              <p><strong>${userName}</strong> (${userEmail}) has been inactive for <strong>${daysInactive} days</strong>.</p>
            </div>
            
            <h3>What this means:</h3>
            <ul>
              <li>${userName} has not logged into their Volnt account recently</li>
              <li>This may trigger their digital will execution process</li>
              <li>As a trusted contact, your verification may be needed</li>
              <li>This is an automated alert to keep you informed</li>
            </ul>
            
            <h3>Recommended Next Steps:</h3>
            <ul>
              <li><strong>Try to contact ${userName} directly</strong> using your usual communication methods</li>
              <li><strong>If you can reach them,</strong> ask them to log into their Volnt account</li>
              <li><strong>If you cannot reach them,</strong> be prepared for potential verification requests</li>
              <li><strong>Keep this email</strong> for your records</li>
            </ul>
            
            ${customMessage ? `<div class="custom-message"><h3>Additional Message:</h3><p>${customMessage}</p></div>` : ''}
            
            <p><strong>Contact Information:</strong><br>
            Email: <a href="mailto:${userEmail}">${userEmail}</a></p>
            
            <p><em>This is an automated alert. No immediate action is required unless you receive a specific verification request.</em></p>
          </div>
          <div class="footer">
            <p>This is an automated alert from Volnt's trigger system.</p>
            <p><strong>Volnt</strong> - Secure your digital legacy</p>
          </div>
        </div>
      </body>
    </html>
  `
}

function generateVerificationRequestEmail(userName: string, userEmail: string, customMessage?: string): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Verification Request</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
          }
          .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: #ffffff; 
          }
          .header { 
            background: linear-gradient(135deg, #7c3aed, #6d28d9); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
          }
          .header h1 { 
            margin: 0; 
            font-size: 24px; 
            font-weight: 600; 
          }
          .content { 
            padding: 30px 20px; 
            background: #ffffff; 
          }
          .verification-box { 
            background: #ede9fe; 
            border: 1px solid #7c3aed; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
          }
          .verification-box h2 { 
            color: #7c3aed; 
            margin-top: 0; 
          }
          .custom-message { 
            background: #f3f4f6; 
            border-left: 4px solid #6b7280; 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 0 8px 8px 0; 
          }
          .button { 
            display: inline-block; 
            background: #7c3aed; 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 8px; 
            margin: 20px 0; 
            font-weight: 500; 
          }
          .footer { 
            padding: 20px; 
            text-align: center; 
            color: #6b7280; 
            font-size: 14px; 
            background: #f9fafb; 
          }
          ul { 
            padding-left: 20px; 
          }
          li { 
            margin: 8px 0; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔍 Verification Required</h1>
          </div>
          <div class="content">
            <h2>Hello,</h2>
            
            <div class="verification-box">
              <h2>Verification Request for ${userName}</h2>
              <p>We need your help to verify the status of <strong>${userName}</strong> (${userEmail}).</p>
            </div>
            
            <h3>Why this verification is needed:</h3>
            <ul>
              <li>${userName} has been inactive for an extended period</li>
              <li>Their digital will execution process has been triggered</li>
              <li>We need trusted contacts to verify their status before proceeding</li>
              <li>This helps prevent accidental will execution</li>
            </ul>
            
            <h3>How to respond:</h3>
            <ul>
              <li><strong>Contact ${userName}</strong> using the information below</li>
              <li><strong>Verify their current status</strong> and wellbeing</li>
              <li><strong>Respond to this verification request</strong> with your findings</li>
              <li><strong>Be honest</strong> about whether you can or cannot reach them</li>
            </ul>
            
            <p><strong>Contact Information:</strong><br>
            Email: <a href="mailto:${userEmail}">${userEmail}</a></p>
            
            ${customMessage ? `<div class="custom-message"><h3>Additional Instructions:</h3><p>${customMessage}</p></div>` : ''}
            
            <div style="text-align: center;">
              <a href="mailto:<EMAIL>?subject=Verification Response for ${userName}&body=I have attempted to contact ${userName}. My findings: " class="button">Respond to Verification</a>
            </div>
            
            <p><em><strong>Important:</strong> Please respond within 48 hours. Your response is crucial for the proper execution of ${userName}'s digital will.</em></p>
          </div>
          <div class="footer">
            <p>This verification request was sent by Volnt's automated system.</p>
            <p><strong>Volnt</strong> - Secure your digital legacy</p>
          </div>
        </div>
      </body>
    </html>
  `
}