{"name": "volnt", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "HOST=0.0.0.0 nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@iconify/vue": "^4.1.2", "@nuxt/icon": "^1.13.0", "@nuxtjs/supabase": "^1.1.6", "@nuxtjs/tailwindcss": "^6.11.2", "@pinia/nuxt": "^0.5.1", "@supabase/supabase-js": "^2.39.3", "@tailwindcss/vite": "^4.0.0", "@tiptap/extension-bubble-menu": "^2.2.3", "@tiptap/extension-document": "^2.2.3", "@tiptap/extension-heading": "^2.2.3", "@tiptap/extension-image": "^2.2.3", "@tiptap/extension-link": "^2.2.3", "@tiptap/extension-paragraph": "^2.2.3", "@tiptap/extension-placeholder": "^2.2.3", "@tiptap/extension-table": "^2.2.3", "@tiptap/extension-task-item": "^2.2.3", "@tiptap/extension-task-list": "^2.2.3", "@tiptap/extension-text": "^2.2.3", "@tiptap/extension-text-style": "^2.2.3", "@tiptap/extension-underline": "^2.2.3", "@tiptap/pm": "^2.2.3", "@tiptap/starter-kit": "^2.2.3", "@tiptap/vue-3": "^2.2.3", "@vueuse/core": "^10.8.0", "crypto-js": "^4.2.0", "daisyui": "^5.0.0", "nuxt": "^3.13.0", "pinia": "^2.1.7", "resend": "^4.5.2", "sortablejs": "^1.15.2", "tailwindcss": "^4.0.0", "vue-draggable-next": "^2.2.1", "zod": "^3.22.4"}, "devDependencies": {"@nuxt/devtools": "latest", "@types/crypto-js": "^4.2.1", "@types/sortablejs": "^1.15.7", "typescript": "^5.3.3"}}