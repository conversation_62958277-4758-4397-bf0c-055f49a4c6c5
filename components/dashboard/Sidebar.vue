<template>
  <div class="bg-base-100 shadow-lg fixed h-full z-20 transition-all duration-300" 
       :class="{ 'w-64': isOpen, 'w-0 md:w-16': !isOpen }">
    <div class="h-full flex flex-col">
      <div class="p-4 flex items-center justify-between">
        <h2 class="font-bold text-lg transition-opacity duration-300"
            :class="{ 'opacity-0 md:opacity-100': !isOpen, 'opacity-100': isOpen }">
          Dashboard
        </h2>
        <button @click="$emit('toggle')" class="btn btn-sm btn-ghost">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" 
               class="w-5 h-5" :class="{ 'rotate-180': !isOpen }">
            <path stroke-linecap="round" stroke-linejoin="round" d="M18.75 19.5l-7.5-7.5 7.5-7.5m-6 15L5.25 12l7.5-7.5" />
          </svg>
        </button>
      </div>
      
      <div class="overflow-y-auto flex-grow">
        <ul class="menu menu-md">
          <li v-for="(item, index) in menuItems" :key="index">
            <NuxtLink :to="item.path" class="flex items-center py-3"
                      :class="{ 'justify-center': !isOpen && !item.submenu }">
              <Icon :name="item.icon" class="w-5 h-5 mx-auto md:mx-0" />
              <span class="ml-3 transition-opacity duration-300" 
                    :class="{ 'hidden md:inline-block': !isOpen, 'opacity-0 md:opacity-100': !isOpen }">
                {{ item.label }}
              </span>
            </NuxtLink>
            <ul v-if="item.submenu && isOpen" class="pl-4">
              <li v-for="(subitem, subindex) in item.submenu" :key="subindex">
                <NuxtLink :to="subitem.path">{{ subitem.label }}</NuxtLink>
              </li>
            </ul>
          </li>
        </ul>
      </div>
      
      <div class="p-4 border-t border-base-300">
        <ClientOnly>
          <div class="flex items-center" :class="{ 'justify-center': !isOpen }">
            <div class="avatar">
              <div class="w-8 rounded-full bg-primary text-white flex items-center justify-center">
                {{ userInitials }}
              </div>
            </div>
            <div class="ml-3 transition-opacity duration-300" 
                 :class="{ 'hidden md:block': !isOpen, 'opacity-0 md:opacity-100': !isOpen }">
              <p class="font-medium">{{ userName }}</p>
              <p class="text-xs text-base-content/60 truncate">{{ userEmail }}</p>
            </div>
          </div>
        </ClientOnly>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Icon } from '@iconify/vue';

defineProps<{
  isOpen: boolean;
}>();

defineEmits(['toggle']);

const user = useSupabaseUser();

const userEmail = computed(() => {
  return user.value?.email || '';
});

const userName = computed(() => {
  return user.value?.user_metadata?.full_name || 'User';
});

const userInitials = computed(() => {
  if (!user.value?.email) return '?';
  return user.value.email.substring(0, 2).toUpperCase();
});

const menuItems = [
  {
    label: 'Overview',
    path: '/dashboard',
    icon: 'mdi:home'
  },
  {
    label: 'Will Editor',
    path: '/dashboard/will',
    icon: 'mdi:file-document-edit'
  },
  {
    label: 'Assets',
    path: '/dashboard/assets',
    icon: 'mdi:treasure-chest',
    submenu: [
      { label: 'Digital', path: '/dashboard/assets/digital' },
      { label: 'Financial', path: '/dashboard/assets/financial' },
      { label: 'Physical', path: '/dashboard/assets/physical' },
      { label: 'Add New', path: '/dashboard/assets/new' }
    ]
  },
  {
    label: 'Beneficiaries',
    path: '/dashboard/beneficiaries',
    icon: 'mdi:account-group'
  },
  {
    label: 'Trigger Settings',
    path: '/dashboard/triggers',
    icon: 'mdi:clock-outline'
  },
  {
    label: 'Security',
    path: '/dashboard/security',
    icon: 'mdi:shield-check'
  },
  {
    label: 'Settings',
    path: '/dashboard/settings',
    icon: 'mdi:cog'
  }
];
</script>