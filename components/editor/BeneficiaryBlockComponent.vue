<template>
  <NodeViewWrapper class="beneficiary-block">
    <div class="card bg-base-100 border border-secondary/20 shadow-sm my-4">
      <div class="card-body p-4">
        <div class="flex items-start justify-between">
          <div class="flex items-center space-x-3">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full bg-secondary/10 text-secondary flex items-center justify-center font-medium">
                {{ getInitials(beneficiary?.name || 'B') }}
              </div>
            </div>
            <div>
              <h4 class="font-semibold text-lg">{{ beneficiary?.name || 'Beneficiary' }}</h4>
              <p class="text-sm text-base-content/70">{{ beneficiary?.relationship || 'Beneficiary' }}</p>
              <div class="flex items-center space-x-2 mt-1">
                <span class="badge badge-secondary badge-sm">
                  {{ formatAccessLevel(beneficiary?.accessLevel || 'LIMITED') }}
                </span>
                <span v-if="beneficiary?.assets?.length" class="text-xs text-base-content/60">
                  {{ beneficiary.assets.length }} assets assigned
                </span>
              </div>
            </div>
          </div>
          
          <div class="dropdown dropdown-end">
            <label tabindex="0" class="btn btn-ghost btn-sm btn-circle">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-********* 0 010 1.5zM12 12.75a.75.75 0 110-********* 0 010 1.5zM12 18.75a.75.75 0 110-********* 0 010 1.5z" />
              </svg>
            </label>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
              <li><a @click="editBeneficiary">Edit Beneficiary</a></li>
              <li><a @click="manageAssets">Manage Assets</a></li>
              <li><a @click="deleteBlock" class="text-error">Remove from Will</a></li>
            </ul>
          </div>
        </div>
        
        <div v-if="beneficiary?.email" class="mt-3">
          <div class="flex items-center text-sm text-base-content/80">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            {{ beneficiary.email }}
          </div>
        </div>
        
        <div v-if="beneficiary?.phoneNumber" class="mt-2">
          <div class="flex items-center text-sm text-base-content/80">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            {{ beneficiary.phoneNumber }}
          </div>
        </div>
        
        <div v-if="beneficiary?.assets?.length" class="mt-3">
          <div class="text-xs font-medium text-base-content/70 mb-2">Assigned Assets:</div>
          <div class="flex flex-wrap gap-1">
            <div 
              v-for="asset in beneficiary.assets.slice(0, 3)" 
              :key="asset.assetId"
              class="badge badge-outline badge-sm"
            >
              {{ getAssetName(asset.assetId) }} ({{ asset.percentage }}%)
            </div>
            <div v-if="beneficiary.assets.length > 3" class="badge badge-outline badge-sm">
              +{{ beneficiary.assets.length - 3 }} more
            </div>
          </div>
        </div>
      </div>
    </div>
  </NodeViewWrapper>
</template>

<script setup lang="ts">
import { NodeViewWrapper } from '@tiptap/vue-3';
import { computed } from 'vue';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { useAssetStore } from '~/stores/assetStore';
import type { Beneficiary, AccessLevel } from '~/types';

const props = defineProps<{
  node: {
    attrs: {
      beneficiaryId: string;
    };
  };
  updateAttributes: (attributes: Record<string, any>) => void;
  deleteNode: () => void;
}>();

const beneficiaryStore = useBeneficiaryStore();
const assetStore = useAssetStore();

const beneficiary = computed(() => {
  return beneficiaryStore.beneficiaries.find(b => b.id === props.node.attrs.beneficiaryId);
});

const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase();
};

const formatAccessLevel = (level: AccessLevel) => {
  const formatMap: Record<AccessLevel, string> = {
    FULL: 'Full Access',
    READ_ONLY: 'Read Only',
    LIMITED: 'Limited'
  };
  
  return formatMap[level] || level;
};

const getAssetName = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  return asset?.name || 'Unknown Asset';
};

const editBeneficiary = () => {
  navigateTo(`/dashboard/beneficiaries/edit/${props.node.attrs.beneficiaryId}`);
};

const manageAssets = () => {
  navigateTo(`/dashboard/beneficiaries/${props.node.attrs.beneficiaryId}/assets`);
};

const deleteBlock = () => {
  props.deleteNode();
};
</script>

<style scoped>
.beneficiary-block {
  margin: 1rem 0;
}
</style>