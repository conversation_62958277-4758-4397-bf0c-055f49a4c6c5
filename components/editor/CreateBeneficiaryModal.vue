<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/30">
    <div class="bg-base-100 w-full sm:w-[500px] rounded-t-2xl sm:rounded-2xl p-4 shadow-lg animate-fade-in-up max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-2">
        <span class="font-bold text-lg">Add New Beneficiary</span>
        <button class="btn btn-xs btn-ghost" @click="close">✕</button>
      </div>
      
      <form @submit.prevent="saveBeneficiary" class="space-y-4">
        <div v-if="errorMessage" class="alert alert-error text-sm">{{ errorMessage }}</div>
        
        <!-- Personal Information -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Personal Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Full Name *</span>
              </label>
              <input 
                type="text" 
                v-model="beneficiaryData.name"
                class="input input-bordered w-full" 
                placeholder="John Doe"
                required
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Email Address *</span>
              </label>
              <input 
                type="email" 
                v-model="beneficiaryData.email"
                class="input input-bordered w-full" 
                placeholder="<EMAIL>"
                required
                @input="updateHasChanges"
              />
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Phone Number</span>
              </label>
              <input 
                type="tel" 
                v-model="beneficiaryData.phoneNumber"
                class="input input-bordered w-full" 
                placeholder="+****************"
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Relationship</span>
              </label>
              <input 
                type="text" 
                v-model="beneficiaryData.relationship"
                class="input input-bordered w-full" 
                placeholder="e.g., Spouse, Child, Friend"
                @input="updateHasChanges"
              />
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Access Level</span>
              </label>
              <select 
                v-model="beneficiaryData.accessLevel" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="LIMITED">Limited - Basic information only</option>
                <option value="READ_ONLY">Read Only - Can view but not modify</option>
                <option value="FULL">Full Access - Can view and manage</option>
              </select>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Notification Preference</span>
              </label>
              <select 
                v-model="beneficiaryData.notificationPreference" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="EMAIL">Email notifications</option>
                <option value="SMS">SMS notifications</option>
                <option value="BOTH">Both email and SMS</option>
                <option value="NONE">No notifications</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-4">
          <button type="button" class="btn btn-ghost" @click="close">Cancel</button>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :disabled="beneficiaryStore.isLoading || !beneficiaryData.name || !beneficiaryData.email"
          >
            {{ beneficiaryStore.isLoading ? 'Saving...' : 'Save Beneficiary' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { AccessLevel, NotificationPreference } from '~/types';

const props = defineProps<{ 
  show: boolean,
  hasChanges?: boolean
}>();

const emit = defineEmits(['close', 'created']);

const beneficiaryStore = useBeneficiaryStore();
const errorMessage = ref('');

// Form data
const beneficiaryData = ref({
  name: '',
  email: '',
  phoneNumber: '',
  relationship: '',
  accessLevel: 'LIMITED' as AccessLevel,
  notificationPreference: 'EMAIL' as NotificationPreference
});

const updateHasChanges = () => {
  emit('close', true);
};

const saveBeneficiary = async () => {
  if (!beneficiaryData.value.name || !beneficiaryData.value.email) return;
  
  try {
    errorMessage.value = '';
    const beneficiary = await beneficiaryStore.createBeneficiary({
      name: beneficiaryData.value.name,
      email: beneficiaryData.value.email,
      phoneNumber: beneficiaryData.value.phoneNumber || undefined,
      relationship: beneficiaryData.value.relationship || undefined,
      accessLevel: beneficiaryData.value.accessLevel,
      notificationPreference: beneficiaryData.value.notificationPreference
    });
    
    if (beneficiary) {
      emit('created', beneficiary);
    }
  } catch (err) {
    console.error('Failed to create beneficiary:', err);
    errorMessage.value = 'Failed to create beneficiary. Please try again.';
  }
};

function close() {
  emit('close', hasFormChanges());
}

function hasFormChanges() {
  return beneficiaryData.value.name !== '' || 
         beneficiaryData.value.email !== '' || 
         beneficiaryData.value.phoneNumber !== '' || 
         beneficiaryData.value.relationship !== '';
}

watch(() => props.show, (val) => {
  if (val) {
    beneficiaryData.value = {
      name: '',
      email: '',
      phoneNumber: '',
      relationship: '',
      accessLevel: 'LIMITED' as AccessLevel,
      notificationPreference: 'EMAIL' as NotificationPreference
    };
    errorMessage.value = '';
  }
});
</script>
