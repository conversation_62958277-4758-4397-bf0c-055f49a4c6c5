<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/30">
    <div class="bg-base-100 w-full sm:w-[600px] rounded-t-2xl sm:rounded-2xl p-4 shadow-lg animate-fade-in-up max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-2">
        <span class="font-bold text-lg">Add New Asset</span>
        <button class="btn btn-xs btn-ghost" @click="close">✕</button>
      </div>
      
      <form @submit.prevent="saveAsset" class="space-y-4">
        <!-- Basic Information -->
        <div class="card bg-base-50 p-4">
          <h3 class="font-semibold text-lg mb-4">Basic Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Asset Name *</span>
              </label>
              <input 
                type="text" 
                v-model="assetData.name"
                class="input input-bordered w-full" 
                placeholder="e.g., Bitcoin Wallet, Gmail Account"
                required
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Asset Type *</span>
              </label>
              <select 
                v-model="assetData.type" 
                class="select select-bordered w-full" 
                required
                @change="updateHasChanges"
              >
                <option value="">Select asset type</option>
                <option v-for="type in assetTypes" :key="type.value" :value="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text">Description</span>
            </label>
            <textarea 
              v-model="assetData.description"
              class="textarea textarea-bordered w-full" 
              placeholder="Brief description of this asset"
              rows="3"
              @input="updateHasChanges"
            ></textarea>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Estimated Value</span>
              </label>
              <input 
                type="number" 
                v-model.number="assetData.value"
                class="input input-bordered w-full" 
                placeholder="0"
                min="0"
                step="0.01"
                @input="updateHasChanges"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Currency</span>
              </label>
              <select 
                v-model="assetData.currency" 
                class="select select-bordered w-full"
                @change="updateHasChanges"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="BTC">BTC</option>
                <option value="ETH">ETH</option>
              </select>
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">Location</span>
              </label>
              <input 
                type="text" 
                v-model="assetData.location"
                class="input input-bordered w-full" 
                placeholder="Physical or digital location"
                @input="updateHasChanges"
              />
            </div>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-4">
          <button type="button" class="btn btn-ghost" @click="close">Cancel</button>
          <button 
            type="submit" 
            class="btn btn-primary" 
            :disabled="assetStore.isLoading || !assetData.name || !assetData.type"
          >
            {{ assetStore.isLoading ? 'Saving...' : 'Save Asset' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useAssetStore } from '~/stores/assetStore';
import type { AssetType } from '~/types';

const props = defineProps<{ 
  show: boolean,
  hasChanges?: boolean
}>();

const emit = defineEmits(['close', 'created', 'changed']);

const assetStore = useAssetStore();

const assetData = ref({
  name: '',
  type: '' as AssetType | '',
  description: '',
  value: undefined as number | undefined,
  currency: 'USD',
  location: ''
});

const assetTypes = [
  { label: 'Digital Account', value: 'DIGITAL_ACCOUNT' },
  { label: 'Cryptocurrency', value: 'CRYPTOCURRENCY' },
  { label: 'Social Media', value: 'SOCIAL_MEDIA' },
  { label: 'Financial Account', value: 'FINANCIAL_ACCOUNT' },
  { label: 'Real Estate', value: 'REAL_ESTATE' },
  { label: 'Vehicle', value: 'VEHICLE' },
  { label: 'Collectible', value: 'COLLECTIBLE' },
  { label: 'Insurance', value: 'INSURANCE' },
  { label: 'Intellectual Property', value: 'INTELLECTUAL_PROPERTY' },
  { label: 'Personal Belonging', value: 'PERSONAL_BELONGING' },
  { label: 'Custom', value: 'CUSTOM' }
];

const updateHasChanges = () => {
  emit('close', true);
};

const saveAsset = async () => {
  if (!assetData.value.name || !assetData.value.type) return;
  
  try {
    const asset = await assetStore.createAsset({
      ...assetData.value,
      type: assetData.value.type as AssetType
    });
    
if (asset) {
       emit('created', asset);
      close();        // close after creation
    }
  } catch (err) {
    console.error('Failed to create asset:', err);
  }
};

function close() {
  emit('close', hasFormChanges());
}

function hasFormChanges() {
  return assetData.value.name !== '' || 
         assetData.value.type !== '' || 
         assetData.value.description !== '' || 
         assetData.value.value !== undefined || 
         assetData.value.location !== '';
}

watch(() => props.show, (val) => {
  if (val) {
    assetData.value = {
      name: '',
      type: '',
      description: '',
      value: undefined,
      currency: 'USD',
      location: ''
    };
  }
});
</script>