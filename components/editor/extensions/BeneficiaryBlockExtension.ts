import { Node, mergeAttributes } from '@tiptap/core';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';

export default Node.create({
  name: 'beneficiaryBlock',
  
  group: 'block',
  
  content: '',
  
  atom: true,
  
  addAttributes() {
    return {
      id: {
        default: null,
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-type="beneficiary-block"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'beneficiary-block' }), 0];
  },
  
  addNodeView() {
    return ({ node }) => {
      const beneficiaryStore = useBeneficiaryStore();
      const beneficiaryId = node.attrs.id;
      const beneficiary = beneficiaryStore.getBeneficiaryById(beneficiaryId);
      
      const dom = document.createElement('div');
      dom.classList.add('beneficiary-block', 'my-2', 'p-3', 'border', 'border-secondary/20', 'bg-secondary/5', 'rounded-lg');
      
      if (beneficiary) {
        // Get initials for avatar
        const initials = beneficiary.name
          .split(' ')
          .map(part => part.charAt(0))
          .join('')
          .toUpperCase()
          .substring(0, 2);
          
        dom.innerHTML = `
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-2">
              <div class="bg-secondary/20 p-2 rounded-full">
                <iconify-icon icon="mdi:account" class="text-secondary" width="20" height="20"></iconify-icon>
              </div>
              <div class="avatar">
                <div class="w-8 h-8 rounded-full bg-secondary/20 text-secondary flex items-center justify-center font-medium text-xs">
                  ${initials}
                </div>
              </div>
            </div>
            <div>
              <div class="font-medium">${beneficiary.name}</div>
              <div class="text-sm text-base-content/70">
                ${beneficiary.relationship ? beneficiary.relationship : 'Beneficiary'}
                ${beneficiary.email ? ` • ${beneficiary.email}` : ''}
              </div>
            </div>
          </div>
        `;
      } else {
        dom.innerHTML = `
          <div class="text-center text-base-content/50">
            <div>Beneficiary not found</div>
            <div class="text-sm">ID: ${beneficiaryId}</div>
          </div>
        `;
      }
      
      return {
        dom,
        contentDOM: null,
      };
    };
  },
});
