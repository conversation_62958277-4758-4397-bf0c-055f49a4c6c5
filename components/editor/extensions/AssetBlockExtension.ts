import { Node, mergeAttributes } from '@tiptap/core';
import { useAssetStore } from '~/stores/assetStore';

export default Node.create({
  name: 'assetBlock',
  
  group: 'block',
  
  content: '',
  
  atom: true,
  
  addAttributes() {
    return {
      id: {
        default: null,
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-type="asset-block"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'asset-block' }), 0];
  },
  
  addNodeView() {
    return ({ node }) => {
      const assetStore = useAssetStore();
      const assetId = node.attrs.id;
      const asset = assetStore.getAssetById(assetId);
      
      const dom = document.createElement('div');
      dom.classList.add('asset-block', 'my-2', 'p-3', 'border', 'border-primary/20', 'bg-primary/5', 'rounded-lg');
      
      if (asset) {
        // Get appropriate icon for asset type
        const getAssetIcon = (type: string) => {
          const iconMap: Record<string, string> = {
            'DIGITAL_ACCOUNT': 'mdi:monitor',
            'CRYPTOCURRENCY': 'mdi:bitcoin',
            'SOCIAL_MEDIA': 'mdi:account-group',
            'FINANCIAL_ACCOUNT': 'mdi:credit-card',
            'REAL_ESTATE': 'mdi:home',
            'VEHICLE': 'mdi:car',
            'COLLECTIBLE': 'mdi:treasure-chest',
            'INSURANCE': 'mdi:shield-check',
            'INTELLECTUAL_PROPERTY': 'mdi:lightbulb',
            'PERSONAL_BELONGING': 'mdi:bag-personal',
            'CUSTOM': 'mdi:tag'
          };
          return iconMap[type] || 'mdi:tag';
        };

        const iconName = getAssetIcon(asset.type);

        dom.innerHTML = `
          <div class="flex items-center gap-2">
            <div class="bg-primary/20 p-2 rounded-full">
              <iconify-icon icon="${iconName}" class="text-primary" width="20" height="20"></iconify-icon>
            </div>
            <div>
              <div class="font-medium">${asset.name}</div>
              <div class="text-sm text-base-content/70">${asset.type.replace(/_/g, ' ').toLowerCase()}</div>
            </div>
          </div>
        `;
      } else {
        dom.innerHTML = `
          <div class="text-center text-base-content/50">
            <div>Asset not found</div>
            <div class="text-sm">ID: ${assetId}</div>
          </div>
        `;
      }
      
      return {
        dom,
        contentDOM: null,
      };
    };
  },
});
