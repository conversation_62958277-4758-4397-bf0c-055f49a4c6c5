<template>
  <div v-if="isOpen" class="modal modal-open">
    <div class="modal-box max-w-4xl">
      <h3 class="font-bold text-lg mb-4">
        Assign Beneficiaries to {{ asset?.name }}
      </h3>

      <!-- Current Assignments -->
      <div v-if="currentAssignments.length > 0" class="mb-6">
        <h4 class="font-semibold mb-3">Current Assignments</h4>
        <div class="space-y-3">
          <div
            v-for="assignment in currentAssignments"
            :key="assignment.beneficiaryId"
            class="flex items-center justify-between p-3 bg-base-200 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div class="avatar placeholder">
                <div class="bg-neutral text-neutral-content rounded-full w-10">
                  <span class="text-sm">{{ getBeneficiaryInitials(assignment.beneficiaryId) }}</span>
                </div>
              </div>
              <span class="font-medium">{{ getBeneficiaryName(assignment.beneficiaryId) }}</span>
            </div>

            <div class="flex items-center space-x-3">
              <div class="flex items-center space-x-2">
                <input
                  v-model.number="assignment.percentage"
                  @input="validatePercentages"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  class="input input-sm input-bordered w-20 text-center"
                />
                <span class="text-sm">%</span>
              </div>
              <button
                @click="removeAssignment(assignment.beneficiaryId)"
                class="btn btn-sm btn-ghost btn-circle text-error"
              >
                <Icon name="mdi:close" class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <!-- Total Percentage -->
        <div class="mt-4 p-3 bg-base-100 rounded-lg border">
          <div class="flex justify-between items-center">
            <span class="font-medium">Total Allocation:</span>
            <span
              class="font-bold text-lg"
              :class="{
                'text-success': totalPercentage <= 100,
                'text-error': totalPercentage > 100
              }"
            >
              {{ totalPercentage.toFixed(1) }}%
            </span>
          </div>
          <div v-if="totalPercentage > 100" class="text-error text-sm mt-1">
            Total allocation cannot exceed 100%
          </div>
          <div v-else-if="totalPercentage < 100" class="text-warning text-sm mt-1">
            {{ (100 - totalPercentage).toFixed(1) }}% remaining
          </div>
        </div>
      </div>

      <!-- Add Beneficiaries -->
      <div class="mb-6">
        <h4 class="font-semibold mb-3">Add Beneficiaries</h4>
        <div v-if="availableBeneficiaries.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div
            v-for="beneficiary in availableBeneficiaries"
            :key="beneficiary.id"
            class="flex items-center justify-between p-3 border border-base-300 rounded-lg hover:bg-base-50"
          >
            <div class="flex items-center space-x-3">
              <div class="avatar placeholder">
                <div class="bg-primary text-primary-content rounded-full w-10">
                  <span class="text-sm">{{ getInitials(beneficiary.name) }}</span>
                </div>
              </div>
              <div>
                <div class="font-medium">{{ beneficiary.name }}</div>
                <div class="text-sm text-base-content/70">{{ beneficiary.relationship }}</div>
              </div>
            </div>
            <button
              @click="addBeneficiary(beneficiary.id)"
              class="btn btn-sm btn-primary"
              :disabled="totalPercentage >= 100"
            >
              <Icon name="mdi:plus" class="h-4 w-4" />
            </button>
          </div>
        </div>

        <div v-if="availableBeneficiaries.length === 0" class="text-center py-6 text-base-content/70">
          <p>All beneficiaries have been assigned to this asset.</p>
          <NuxtLink to="/dashboard/beneficiaries/new" class="btn btn-sm btn-outline mt-2">
            Add New Beneficiary
          </NuxtLink>
        </div>
      </div>

      <!-- Actions -->
      <div class="modal-action">
        <button @click="closeModal" class="btn">Cancel</button>
        <button
          @click="saveAssignments"
          class="btn btn-primary"
          :class="{ 'loading': isLoading }"
          :disabled="totalPercentage > 100 || isLoading"
        >
          Save Assignments
        </button>
      </div>
    </div>
    <div class="modal-backdrop" @click="closeModal"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Icon } from '@iconify/vue';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import type { Asset, Beneficiary } from '~/types';

interface Assignment {
  beneficiaryId: string;
  percentage: number;
}

const props = defineProps<{
  isOpen: boolean;
  asset: Asset | null;
}>();

const emit = defineEmits(['close', 'saved']);

const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

const isLoading = ref(false);
const currentAssignments = ref<Assignment[]>([]);

// Computed properties
const availableBeneficiaries = computed(() => {
  const assignedIds = currentAssignments.value.map(a => a.beneficiaryId);
  return beneficiaryStore.beneficiaries.filter(b => !assignedIds.includes(b.id));
});

const totalPercentage = computed(() => {
  return currentAssignments.value.reduce((sum, assignment) => sum + (assignment.percentage || 0), 0);
});

// Helper functions
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase();
};

const getBeneficiaryName = (beneficiaryId: string) => {
  const beneficiary = beneficiaryStore.beneficiaries.find(b => b.id === beneficiaryId);
  return beneficiary?.name || 'Unknown';
};

const getBeneficiaryInitials = (beneficiaryId: string) => {
  const name = getBeneficiaryName(beneficiaryId);
  return getInitials(name);
};

// Actions
const addBeneficiary = (beneficiaryId: string) => {
  const remainingPercentage = 100 - totalPercentage.value;
  const defaultPercentage = Math.min(remainingPercentage, 25);

  currentAssignments.value.push({
    beneficiaryId,
    percentage: defaultPercentage
  });
};

const removeAssignment = (beneficiaryId: string) => {
  currentAssignments.value = currentAssignments.value.filter(a => a.beneficiaryId !== beneficiaryId);
};

const validatePercentages = () => {
  // Ensure no negative values
  currentAssignments.value.forEach(assignment => {
    if (assignment.percentage < 0) assignment.percentage = 0;
    if (assignment.percentage > 100) assignment.percentage = 100;
  });
};

const saveAssignments = async () => {
  if (!props.asset || totalPercentage.value > 100) return;

  isLoading.value = true;

  try {
    // Remove all existing assignments for this asset
    const existingAssignments = props.asset.beneficiaries || [];

    // Create maps for efficient lookup
    const existingMap = new Map(existingAssignments.map(b => [b.id, b.percentage || 0]));
    const newMap = new Map(currentAssignments.value.map(a => [a.beneficiaryId, a.percentage]));

    // Remove assignments that no longer exist or have 0%
    for (const [beneficiaryId, oldPercentage] of existingMap) {
      const newPercentage = newMap.get(beneficiaryId) || 0;
      if (newPercentage === 0) {
        await beneficiaryStore.removeAssetAssignment(beneficiaryId, props.asset.id);
      }
    }

    // Add or update assignments
    for (const assignment of currentAssignments.value) {
      if (assignment.percentage > 0) {
        const oldPercentage = existingMap.get(assignment.beneficiaryId) || 0;
        if (oldPercentage !== assignment.percentage) {
          await beneficiaryStore.assignAsset(assignment.beneficiaryId, props.asset.id, assignment.percentage);
        }
      } else {
        // Show notification that 0% assignments are removed
        console.warn(`Beneficiary ${assignment.beneficiaryId} removed due to 0% allocation`);
      }
    }

    // Refresh data
    await assetStore.fetchAssets();
    await beneficiaryStore.fetchBeneficiaries();

    emit('saved');
    closeModal();
  } catch (error) {
    console.error('Error saving assignments:', error);
  } finally {
    isLoading.value = false;
  }
};

const closeModal = () => {
  emit('close');
};

// Watch for asset changes to load current assignments
watch(() => props.asset, (newAsset) => {
  if (newAsset) {
    currentAssignments.value = (newAsset.beneficiaries || []).map(b => ({
      beneficiaryId: b.id,
      percentage: b.percentage || 0
    }));
  } else {
    currentAssignments.value = [];
  }
}, { immediate: true });
</script>