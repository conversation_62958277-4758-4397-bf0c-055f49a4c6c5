import { defineStore } from 'pinia';
import { useSupabaseClient } from '#imports';
import type { Beneficiary, AccessLevel, NotificationPreference } from '~/types';

interface BeneficiaryState {
  beneficiaries: Beneficiary[];
  isLoading: boolean;
  currentBeneficiary: Beneficiary | null;
}

export const useBeneficiaryStore = defineStore('beneficiary', {
  state: (): BeneficiaryState => ({
    beneficiaries: [],
    isLoading: false,
    currentBeneficiary: null
  }),
  
  getters: {
    primaryBeneficiaries: (state) => {
      return state.beneficiaries.filter(ben => ben.relationship === 'Spouse' || ben.relationship === 'Child' || ben.relationship === 'Parent');
    },
    
    otherBeneficiaries: (state) => {
      return state.beneficiaries.filter(ben => !['Spouse', 'Child', 'Parent'].includes(ben.relationship || ''));
    },
    
    totalBeneficiaries: (state) => {
      return state.beneficiaries.length;
    },
    
    assignedBeneficiaries: (state) => {
      return state.beneficiaries.filter(ben => ben.assets && ben.assets.length > 0);
    },

    getBeneficiaryById: (state) => {
      return (id: string) => state.beneficiaries.find(ben => ben.id === id);
    }
  },
  
  actions: {
    async fetchBeneficiaries() {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        // Fetch beneficiaries
        const { data: beneficiaryData, error: beneficiaryError } = await supabase
          .from('beneficiaries')
          .select('*');
        
        if (beneficiaryError) throw beneficiaryError;
        
        // Fetch asset assignments
        const { data: assignmentData, error: assignmentError } = await supabase
          .from('asset_beneficiaries')
          .select('*');
        
        if (assignmentError) throw assignmentError;
        
        // Map asset assignments to beneficiaries
        this.beneficiaries = beneficiaryData.map((beneficiary: any) => {
          const assets = assignmentData
            .filter((a: any) => a.beneficiary_id === beneficiary.id)
            .map((a: any) => ({
              assetId: a.asset_id,
              percentage: a.percentage
            }));
          
          return {
            id: beneficiary.id,
            userId: beneficiary.user_id,
            name: beneficiary.name,
            email: beneficiary.email,
            phoneNumber: beneficiary.phone_number,
            relationship: beneficiary.relationship,
            accessLevel: beneficiary.access_level as AccessLevel,
            assets: assets,
            notificationPreference: beneficiary.notification_preference as NotificationPreference,
            createdAt: beneficiary.created_at,
            updatedAt: beneficiary.updated_at
          };
        });
        
      } catch (error) {
        console.error('Error fetching beneficiaries:', error);
      } finally {
        this.isLoading = false;
      }
    },
    
    async fetchBeneficiary(beneficiaryId: string) {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        // Fetch beneficiary
        const { data: beneficiary, error: beneficiaryError } = await supabase
          .from('beneficiaries')
          .select('*')
          .eq('id', beneficiaryId)
          .single();
        
        if (beneficiaryError) throw beneficiaryError;
        
        // Fetch asset assignments
        const { data: assignmentData, error: assignmentError } = await supabase
          .from('asset_beneficiaries')
          .select('*')
          .eq('beneficiary_id', beneficiaryId);
        
        if (assignmentError) throw assignmentError;
        
        const assets = assignmentData.map((a: any) => ({
          assetId: a.asset_id,
          percentage: a.percentage
        }));
        
        this.currentBeneficiary = {
          id: beneficiary.id,
          userId: beneficiary.user_id,
          name: beneficiary.name,
          email: beneficiary.email,
          phoneNumber: beneficiary.phone_number,
          relationship: beneficiary.relationship,
          accessLevel: beneficiary.access_level as AccessLevel,
          assets: assets,
          notificationPreference: beneficiary.notification_preference as NotificationPreference,
          createdAt: beneficiary.created_at,
          updatedAt: beneficiary.updated_at
        };
        
        return this.currentBeneficiary;
      } catch (error) {
        console.error('Error fetching beneficiary:', error);
        return null;
      } finally {
        this.isLoading = false;
      }
    },
    
    async createBeneficiary(beneficiary: Partial<Beneficiary>) {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('User not authenticated');
        
        const { data, error } = await supabase
          .from('beneficiaries')
          .insert({
            user_id: user.id,
            name: beneficiary.name,
            email: beneficiary.email,
            phone_number: beneficiary.phoneNumber,
            relationship: beneficiary.relationship,
            access_level: beneficiary.accessLevel || 'LIMITED',
            notification_preference: beneficiary.notificationPreference || 'EMAIL'
          })
          .select()
          .single();
        
        if (error) throw error;
        
        // Add beneficiary to store
        const newBeneficiary: Beneficiary = {
          id: data.id,
          userId: data.user_id,
          name: data.name,
          email: data.email,
          phoneNumber: data.phone_number,
          relationship: data.relationship,
          accessLevel: data.access_level as AccessLevel,
          assets: [],
          notificationPreference: data.notification_preference as NotificationPreference,
          createdAt: data.created_at,
          updatedAt: data.updated_at
        };
        
        this.beneficiaries.push(newBeneficiary);
        return newBeneficiary;
        
      } catch (error) {
        console.error('Error creating beneficiary:', error);
        return null;
      } finally {
        this.isLoading = false;
      }
    },
    
    async updateBeneficiary(beneficiaryId: string, beneficiaryData: Partial<Beneficiary>) {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        const { data, error } = await supabase
          .from('beneficiaries')
          .update({
            name: beneficiaryData.name,
            email: beneficiaryData.email,
            phone_number: beneficiaryData.phoneNumber,
            relationship: beneficiaryData.relationship,
            access_level: beneficiaryData.accessLevel,
            notification_preference: beneficiaryData.notificationPreference,
            updated_at: new Date().toISOString()
          })
          .eq('id', beneficiaryId)
          .select()
          .single();
        
        if (error) throw error;
        
        // Update beneficiary in store
        const index = this.beneficiaries.findIndex(b => b.id === beneficiaryId);
        if (index !== -1) {
          this.beneficiaries[index] = {
            ...this.beneficiaries[index],
            name: data.name,
            email: data.email,
            phoneNumber: data.phone_number,
            relationship: data.relationship,
            accessLevel: data.access_level as AccessLevel,
            notificationPreference: data.notification_preference as NotificationPreference,
            updatedAt: data.updated_at
          };
        }
        
        return data;
        
      } catch (error) {
        console.error('Error updating beneficiary:', error);
        return null;
      } finally {
        this.isLoading = false;
      }
    },
    
    async deleteBeneficiary(beneficiaryId: string) {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        const { error } = await supabase
          .from('beneficiaries')
          .delete()
          .eq('id', beneficiaryId);
        
        if (error) throw error;
        
        // Remove beneficiary from store
        this.beneficiaries = this.beneficiaries.filter(b => b.id !== beneficiaryId);
        return true;
        
      } catch (error) {
        console.error('Error deleting beneficiary:', error);
        return false;
      } finally {
        this.isLoading = false;
      }
    },
    
    async assignAsset(beneficiaryId: string, assetId: string, percentage: number) {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        // Check if assignment already exists
        const { data: existing, error: checkError } = await supabase
          .from('asset_beneficiaries')
          .select('*')
          .eq('beneficiary_id', beneficiaryId)
          .eq('asset_id', assetId);
        
        if (checkError) throw checkError;
        
        let result;
        
        if (existing && existing.length > 0) {
          // Update existing assignment
          const { data, error } = await supabase
            .from('asset_beneficiaries')
            .update({ 
              percentage,
              updated_at: new Date().toISOString()
            })
            .eq('beneficiary_id', beneficiaryId)
            .eq('asset_id', assetId)
            .select();
          
          if (error) throw error;
          result = data;
        } else {
          // Create new assignment
          const { data, error } = await supabase
            .from('asset_beneficiaries')
            .insert({
              beneficiary_id: beneficiaryId,
              asset_id: assetId,
              percentage
            })
            .select();
          
          if (error) throw error;
          result = data;
        }
        
        // Update local state
        const beneficiary = this.beneficiaries.find(b => b.id === beneficiaryId);
        if (beneficiary) {
          const existingAssetIndex = beneficiary.assets.findIndex(a => a.assetId === assetId);
          
          if (existingAssetIndex !== -1) {
            // Update existing asset
            beneficiary.assets[existingAssetIndex].percentage = percentage;
          } else {
            // Add new asset
            beneficiary.assets.push({
              assetId,
              percentage
            });
          }
        }
        
        return result;
        
      } catch (error) {
        console.error('Error assigning asset to beneficiary:', error);
        return null;
      } finally {
        this.isLoading = false;
      }
    },
    
    async removeAssetAssignment(beneficiaryId: string, assetId: string) {
      const supabase = useSupabaseClient();
      this.isLoading = true;
      
      try {
        const { error } = await supabase
          .from('asset_beneficiaries')
          .delete()
          .eq('beneficiary_id', beneficiaryId)
          .eq('asset_id', assetId);
        
        if (error) throw error;
        
        // Update local state
        const beneficiary = this.beneficiaries.find(b => b.id === beneficiaryId);
        if (beneficiary) {
          beneficiary.assets = beneficiary.assets.filter(a => a.assetId !== assetId);
        }
        
        return true;
        
      } catch (error) {
        console.error('Error removing asset assignment:', error);
        return false;
      } finally {
        this.isLoading = false;
      }
    }
  }
});