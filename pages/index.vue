<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <section class="relative py-20 bg-gradient-to-r from-primary-900 to-primary-700 text-white overflow-hidden">
      <div class="absolute inset-0 opacity-20">
        <div class="absolute inset-0 bg-[url('https://images.pexels.com/photos/7135073/pexels-photo-7135073.jpeg')] bg-cover bg-center"></div>
      </div>
      
      <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-3xl mx-auto text-center">
          <div class="flex items-center justify-center mb-6">
            <img src="/images/volnt.svg" alt="Volnt" class="h-16 w-16 mr-4" />
            <h1 class="text-4xl md:text-5xl font-bold animate-fade-in">
              Volnt
            </h1>
          </div>
          <p class="text-lg md:text-xl mb-8 text-white/90 animate-fade-in" style="animation-delay: 0.1s;">
            Secure your digital legacy with our comprehensive will and testament platform. From the Latin "voluntas" meaning will.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in" style="animation-delay: 0.2s;">
            <NuxtLink to="/auth/register" class="btn btn-accent btn-lg">
              Get Started
            </NuxtLink>
            <NuxtLink to="/about" class="btn btn-outline btn-lg text-white border-white hover:bg-white hover:text-primary-700">
              Learn More
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-base-100">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Comprehensive Digital Legacy Management</h2>
          <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
            Our platform offers everything you need to document, protect, and transfer your digital and physical assets.
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(feature, index) in features" :key="index" class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow">
            <div class="card-body">
              <div class="bg-primary/10 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Icon :name="feature.icon" class="w-6 h-6 text-primary" />
              </div>
              <h3 class="text-xl font-semibold mb-2">{{ feature.title }}</h3>
              <p class="text-base-content/70">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works -->
    <section class="py-16 bg-base-200">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">How It Works</h2>
          <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
            Our streamlined process makes creating your digital will simple and secure.
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div v-for="(step, index) in steps" :key="index" 
               class="card bg-base-100 shadow-md relative overflow-hidden">
            <div class="card-body">
              <div class="absolute top-4 right-4 bg-primary text-white w-8 h-8 rounded-full flex items-center justify-center font-bold">
                {{ index + 1 }}
              </div>
              <h3 class="font-semibold text-lg mb-2">{{ step.title }}</h3>
              <p class="text-sm text-base-content/70">{{ step.description }}</p>
            </div>
          </div>
        </div>
        
        <div class="text-center mt-10">
          <NuxtLink to="/auth/register" class="btn btn-primary">Start Creating Your Will</NuxtLink>
        </div>
      </div>
    </section>

    <!-- Testimonial Section -->
    <section class="py-16 bg-base-100">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">What Our Users Say</h2>
          <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
            Join thousands of people who have already secured their digital legacy with Volnt.
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="(testimonial, index) in testimonials" :key="index" 
               class="card bg-base-200 shadow-md hover:shadow-lg transition-shadow">
            <div class="card-body">
              <div class="flex items-center space-x-1 mb-4">
                <Icon v-for="i in 5" :key="i" name="mdi:star" class="h-4 w-4" :class="i <= testimonial.rating ? 'text-yellow-400' : 'text-gray-300'" />
              </div>
              <p class="italic mb-4">{{ testimonial.quote }}</p>
              <div class="flex items-center">
                <div class="avatar">
                  <div class="w-10 rounded-full">
                    <img :src="testimonial.avatarUrl" :alt="testimonial.name" />
                  </div>
                </div>
                <div class="ml-3">
                  <p class="font-medium">{{ testimonial.name }}</p>
                  <p class="text-xs text-base-content/60">{{ testimonial.title }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- CTA Section -->
    <section class="py-16 bg-gradient-to-r from-primary-700 to-primary-900 text-white">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center">
          <h2 class="text-3xl font-bold mb-4">Ready to Secure Your Digital Legacy?</h2>
          <p class="text-lg mb-8 text-white/90">
            Create your digital will today with Volnt and ensure your digital assets are protected for generations to come.
          </p>
          <NuxtLink to="/auth/register" class="btn btn-accent btn-lg">
            Get Started Now
          </NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue';

useHead({
  title: 'Volnt - Digital Will & Testament',
  meta: [
    {
      name: 'description',
      content: 'Volnt - Create and manage your digital will to protect your digital and physical assets for future generations.'
    }
  ]
});

const features = [
  {
    title: 'Complete Asset Management',
    description: 'Document and manage both your digital and physical assets in one secure location.',
    icon: 'mdi:treasure-chest'
  },
  {
    title: 'AI-Assisted Document Creation',
    description: 'Our AI assistant helps you create a comprehensive will with smart templates and suggestions.',
    icon: 'mdi:robot'
  },
  {
    title: 'Beneficiary Management',
    description: 'Easily designate beneficiaries and specify exactly what they will receive and when.',
    icon: 'mdi:account-group'
  },
  {
    title: 'End-to-End Encryption',
    description: 'Your sensitive information is encrypted with the highest security standards.',
    icon: 'mdi:lock'
  },
  {
    title: 'Customizable Trigger System',
    description: 'Set up conditions that will trigger the execution of your will when needed.',
    icon: 'mdi:clock-outline'
  },
  {
    title: 'Legal Compliance',
    description: 'Our system adapts to legal requirements in your jurisdiction for maximum validity.',
    icon: 'mdi:gavel'
  }
];

const steps = [
  {
    title: 'Create Account',
    description: 'Sign up and secure your account with multi-factor authentication for maximum protection.'
  },
  {
    title: 'Document Assets',
    description: 'Add your digital and physical assets with detailed information and secure credentials.'
  },
  {
    title: 'Designate Beneficiaries',
    description: 'Select who will receive your assets and what level of access they will have.'
  },
  {
    title: 'Set Trigger Conditions',
    description: 'Configure when and how your will should be executed with customizable triggers.'
  }
];

const testimonials = [
  {
    name: 'Sarah Johnson',
    title: 'Tech Professional',
    rating: 5,
    quote: 'Volnt gave me peace of mind knowing my cryptocurrency keys and digital accounts are secure but accessible to my family when needed.',
    avatarUrl: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=300'
  },
  {
    name: 'Michael Rodriguez',
    title: 'Estate Attorney',
    rating: 5,
    quote: 'As an estate attorney, I recommend Volnt to all my clients. It fills a critical gap in traditional estate planning for digital assets.',
    avatarUrl: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=300'
  },
  {
    name: 'Emily Chen',
    title: 'Small Business Owner',
    rating: 4,
    quote: 'The intuitive interface made it easy to organize my business assets and ensure my family would have access to crucial accounts.',
    avatarUrl: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=300'
  }
];
</script>