<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Asset Management</h1>
        <p class="text-base-content/70">Manage your digital and physical assets</p>
      </div>
      
      <div class="flex space-x-2">
        <NuxtLink to="/dashboard/assets/new" class="btn btn-sm btn-primary">
          <Icon name="mdi:plus" class="h-4 w-4 mr-1" />
          Add Asset
        </NuxtLink>
      </div>
    </div>
    
    <!-- Asset Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="stat bg-base-100 shadow-md rounded-box">
        <div class="stat-title">Total Assets</div>
        <div class="stat-value">{{ assetStore.assets.length }}</div>
        <div class="stat-desc">{{ assetStore.digitalAssets.length }} digital, {{ assetStore.physicalAssets.length }} physical</div>
      </div>
      
      <div class="stat bg-base-100 shadow-md rounded-box">
        <div class="stat-title">Total Value</div>
        <div class="stat-value">{{ formatCurrency(assetStore.totalAssetValue) }}</div>
        <div class="stat-desc flex items-center">
          <Icon name="mdi:trending-up" class="h-4 w-4 mr-1 text-success" />
          Assets documented
        </div>
      </div>
      
      <div class="stat bg-base-100 shadow-md rounded-box">
        <div class="stat-title">Assigned Assets</div>
        <div class="stat-value">{{ assetStore.assignedAssets.length }}/{{ assetStore.assets.length }}</div>
        <div class="stat-desc">Assets with beneficiaries</div>
      </div>
    </div>
    
    <!-- Allocation Summary -->
    <UiAllocationSummary
      v-if="assetStore.assets.length > 0"
      :assets="assetStore.assets"
      :assignments="allAssignments"
      show-actions
      class="mb-6"
      @assign-asset="openAssignModalForAsset"
      @manage-asset="manageBeneficiaries"
      @auto-allocate="autoAllocateAssets"
      @view-report="viewAllocationReport"
    />

    <!-- Asset Category Tabs -->
    <div class="tabs mb-4">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        class="tab tab-bordered"
        :class="{ 'tab-active': activeTab === tab.value }"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>
    
    <!-- Search and Filter -->
    <div class="flex flex-col md:flex-row gap-4 mb-6">
      <div class="form-control flex-1">
        <div class="input-group">
          <input 
            type="text" 
            v-model="searchQuery"
            placeholder="Search assets..." 
            class="input input-bordered w-full"
          />
          <button class="btn btn-square">
            <Icon name="mdi:magnify" class="h-5 w-5" />
          </button>
        </div>
      </div>
      
      <select v-model="typeFilter" class="select select-bordered">
        <option value="">All Asset Types</option>
        <option v-for="type in assetTypes" :key="type.value" :value="type.value">
          {{ type.label }}
        </option>
      </select>
      
      <select v-model="sortBy" class="select select-bordered">
        <option value="name">Sort by Name</option>
        <option value="value">Sort by Value</option>
        <option value="date">Sort by Date Added</option>
      </select>
    </div>
    
    <!-- Loading State -->
    <div v-if="assetStore.isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <!-- Assets Grid -->
    <div v-else-if="filteredAssets.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
      <UiAssetCard
        v-for="asset in filteredAssets"
        :key="asset.id"
        :asset="asset"
        show-beneficiary-count
        @edit="editAsset"
        @delete="confirmDeleteAsset"
        @view-details="viewAssetDetails"
        @assign-beneficiaries="openAssignModal"
        @manage-beneficiaries="manageBeneficiaries"
      />
    </div>
    
    <!-- Empty State -->
    <div v-else class="bg-base-100 rounded-lg p-8 text-center">
      <div class="mb-4 flex justify-center">
        <div class="rounded-full bg-primary/10 p-3">
          <Icon name="mdi:treasure-chest" class="h-12 w-12 text-primary" />
        </div>
      </div>
      <h3 class="text-xl font-medium mb-2">No assets found</h3>
      <p class="text-base-content/70 mb-6">
        {{ activeTab === 'all' 
          ? "You haven't added any assets yet." 
          : `You haven't added any ${activeTab} assets yet.` }}
      </p>
      <NuxtLink to="/dashboard/assets/new" class="btn btn-primary">
        Add Your First Asset
      </NuxtLink>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal" :class="{ 'modal-open': showDeleteModal }">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Confirm Delete</h3>
        <p>Are you sure you want to delete this asset? This action cannot be undone.</p>
        <div class="modal-action">
          <button @click="showDeleteModal = false" class="btn">Cancel</button>
          <button @click="deleteAsset" class="btn btn-error" :class="{ 'loading': assetStore.isLoading }">
            Delete
          </button>
        </div>
      </div>
      <div class="modal-backdrop" @click="showDeleteModal = false"></div>
    </div>

    <!-- Asset Beneficiary Assignment Modal -->
    <AssetBeneficiaryModal
      :is-open="showAssignModal"
      :asset="selectedAsset"
      @close="closeAssignModal"
      @saved="onAssignmentSaved"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import AssetBeneficiaryModal from '~/components/ui/AssetBeneficiaryModal.vue';
import type { Asset, AssetType } from '~/types';

definePageMeta({
  layout: 'dashboard'
});

const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

// Tabs
const tabs = [
  { label: 'All Assets', value: 'all' },
  { label: 'Digital', value: 'digital' },
  { label: 'Financial', value: 'financial' },
  { label: 'Physical', value: 'physical' }
];
const activeTab = ref('all');

// Filters
const searchQuery = ref('');
const typeFilter = ref('');
const sortBy = ref('name');

// Modals
const showDeleteModal = ref(false);
const assetToDelete = ref<string | null>(null);
const showAssignModal = ref(false);
const selectedAsset = ref<Asset | null>(null);

// Computed property for all assignments
const allAssignments = computed(() => {
  const assignments: Array<{ assetId: string; beneficiaryId: string; percentage: number }> = [];

  assetStore.assets.forEach(asset => {
    asset.beneficiaries?.forEach(beneficiary => {
      assignments.push({
        assetId: asset.id,
        beneficiaryId: beneficiary.id,
        percentage: beneficiary.percentage || 0
      });
    });
  });

  return assignments;
});

// Asset types for dropdown
const assetTypes = [
  { label: 'Digital Account', value: 'DIGITAL_ACCOUNT' },
  { label: 'Cryptocurrency', value: 'CRYPTOCURRENCY' },
  { label: 'Social Media', value: 'SOCIAL_MEDIA' },
  { label: 'Financial Account', value: 'FINANCIAL_ACCOUNT' },
  { label: 'Real Estate', value: 'REAL_ESTATE' },
  { label: 'Vehicle', value: 'VEHICLE' },
  { label: 'Collectible', value: 'COLLECTIBLE' },
  { label: 'Insurance', value: 'INSURANCE' },
  { label: 'Intellectual Property', value: 'INTELLECTUAL_PROPERTY' },
  { label: 'Personal Belonging', value: 'PERSONAL_BELONGING' },
  { label: 'Custom', value: 'CUSTOM' }
];

// Filter assets based on active tab, search query, and type filter
const filteredAssets = computed(() => {
  let result = [...assetStore.assets];
  
  // Filter by tab
  if (activeTab.value !== 'all') {
    const tabFilters = {
      digital: ['DIGITAL_ACCOUNT', 'CRYPTOCURRENCY', 'SOCIAL_MEDIA'],
      financial: ['FINANCIAL_ACCOUNT', 'INSURANCE'],
      physical: ['REAL_ESTATE', 'VEHICLE', 'COLLECTIBLE', 'PERSONAL_BELONGING']
    };
    
    const applicableTypes = tabFilters[activeTab.value as keyof typeof tabFilters] || [];
    if (applicableTypes.length > 0) {
      result = result.filter(asset => applicableTypes.includes(asset.type));
    }
  }
  
  // Filter by type
  if (typeFilter.value) {
    result = result.filter(asset => asset.type === typeFilter.value);
  }
  
  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(asset => 
      asset.name.toLowerCase().includes(query) || 
      asset.description?.toLowerCase().includes(query) ||
      asset.accountDetails?.provider?.toLowerCase().includes(query)
    );
  }
  
  // Sort results
  result.sort((a, b) => {
    if (sortBy.value === 'name') {
      return a.name.localeCompare(b.name);
    } else if (sortBy.value === 'value') {
      return (b.value || 0) - (a.value || 0);
    } else if (sortBy.value === 'date') {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
    return 0;
  });
  
  return result;
});

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0
  }).format(value);
};

// Asset operations
const editAsset = (id: string) => {
  navigateTo(`/dashboard/assets/edit/${id}`);
};

const viewAssetDetails = (id: string) => {
  navigateTo(`/dashboard/assets/${id}`);
};

const openAssignModal = (id: string) => {
  selectedAsset.value = assetStore.assets.find(a => a.id === id) || null;
  showAssignModal.value = true;
};

const closeAssignModal = () => {
  showAssignModal.value = false;
  selectedAsset.value = null;
};

const onAssignmentSaved = () => {
  // Data will be refreshed by the modal
};

const openAssignModalForAsset = (id: string) => {
  openAssignModal(id);
};

const manageBeneficiaries = (id: string) => {
  navigateTo(`/dashboard/assets/${id}/beneficiaries`);
};

const autoAllocateAssets = () => {
  // TODO: Implement auto-allocation logic
  console.log('Auto-allocate assets');
};

const viewAllocationReport = () => {
  // TODO: Navigate to allocation report page
  navigateTo('/dashboard/reports/allocation');
};

const confirmDeleteAsset = (id: string) => {
  assetToDelete.value = id;
  showDeleteModal.value = true;
};

const deleteAsset = async () => {
  if (assetToDelete.value) {
    const success = await assetStore.deleteAsset(assetToDelete.value);
    if (success) {
      showDeleteModal.value = false;
      assetToDelete.value = null;
    }
  }
};

// Fetch data on mount
onMounted(async () => {
  await assetStore.fetchAssets();
  await beneficiaryStore.fetchBeneficiaries();
});
</script>