<template>
  <div>
    <OnboardingWizard v-if="showOnboarding" @finished="showOnboarding = false" @dismissed="showOnboarding = false" />
    <button @click="openOnboarding" class="fixed z-40 bottom-4 right-4 md:left-4 md:right-auto btn btn-circle btn-primary shadow-lg" title="Show onboarding/help">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 14v.01M16 10h.01M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" /></svg>
    </button>
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 gap-2 sm:gap-0">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold">Welcome, {{ userName }}</h1>
        <p class="text-base-content/70 text-sm sm:text-base">Here's an overview of your digital will status</p>
      </div>
      <div class="flex flex-row gap-2 mt-2 sm:mt-0">
        <button class="btn btn-xs sm:btn-sm btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Export
        </button>
        <NuxtLink to="/dashboard/will" class="btn btn-xs sm:btn-sm btn-primary">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Edit Will
        </NuxtLink>
      </div>
    </div>
    
    <!-- Overview Cards -->
    <div class="flex flex-nowrap gap-4 mb-4 sm:mb-6 overflow-x-auto pb-2 hide-scrollbar md:grid md:grid-cols-3 md:gap-4 md:overflow-visible md:pb-0">
      <div class="stat min-w-[220px] bg-base-100 shadow-md rounded-box">
        <div class="stat-title text-xs sm:text-sm">Will Completion</div>
        <div class="stat-value text-primary text-lg sm:text-2xl">{{ completionPercentage }}%</div>
        <div class="stat-desc">
          <progress class="progress progress-primary w-full" :value="completionPercentage" max="100"></progress>
        </div>
      </div>
      
      <div class="stat min-w-[220px] bg-base-100 shadow-md rounded-box">
        <div class="stat-title text-xs sm:text-sm">Total Assets</div>
        <div class="stat-value text-lg sm:text-2xl">{{ assetStore.assets.length }}</div>
        <div class="stat-desc text-xs sm:text-sm">{{ assetStore.digitalAssets.length }} digital, {{ assetStore.physicalAssets.length }} physical</div>
      </div>
      
      <div class="stat min-w-[220px] bg-base-100 shadow-md rounded-box">
        <div class="stat-title text-xs sm:text-sm">Beneficiaries</div>
        <div class="stat-value text-lg sm:text-2xl">{{ beneficiaryStore.beneficiaries.length }}</div>
        <div class="stat-desc text-xs sm:text-sm">{{ beneficiaryStore.assignedBeneficiaries.length }} of {{ beneficiaryStore.beneficiaries.length }} assigned</div>
      </div>
    </div>
    
    <!-- Completion Cards -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3 md:gap-6 mb-6 md:mb-8">
      <DashboardCompletionCard
        title="Will Document"
        description="Complete your core will document"
        :percentage="willDocumentCompletion"
        :items="willTasks"
        action-text="Edit Document"
        action-link="/dashboard/will"
        @toggle-item="toggleTaskCompletion"
      />
      
      <DashboardCompletionCard
        title="Asset Inventory"
        description="Document your digital and physical assets"
        :percentage="assetCompletion"
        :items="assetTasks"
        action-text="Manage Assets"
        action-link="/dashboard/assets"
        @toggle-item="toggleTaskCompletion"
      />
      
      <DashboardCompletionCard
        title="Beneficiary Setup"
        description="Assign assets to your beneficiaries"
        :percentage="beneficiaryCompletion"
        :items="beneficiaryTasks"
        action-text="Manage Beneficiaries"
        action-link="/dashboard/beneficiaries"
        @toggle-item="toggleTaskCompletion"
      />
    </div>
    
    <!-- Recent Activity -->
    <div class="bg-base-100 shadow-md rounded-box p-2 sm:p-4 mb-6 sm:mb-8">
      <h2 class="text-base sm:text-lg font-semibold mb-2 sm:mb-4">Recent Activity</h2>
      
      <div v-if="recentActivity.length > 0" class="overflow-x-auto">
        <table class="table table-zebra w-full text-xs sm:text-sm">
          <thead>
            <tr>
              <th>Date</th>
              <th>Activity</th>
              <th>Details</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(activity, index) in recentActivity" :key="index">
              <td class="whitespace-nowrap">{{ formatDate(activity.timestamp) }}</td>
              <td>{{ activity.action }}</td>
              <td class="max-w-md truncate">{{ activity.details }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div v-else class="text-center py-6 sm:py-8">
        <p class="text-base-content/70 text-xs sm:text-base">No recent activity</p>
      </div>
    </div>
    
    <!-- Security Status -->
    <div class="bg-base-100 shadow-md rounded-box p-2 sm:p-4">
      <h2 class="text-base sm:text-lg font-semibold mb-2 sm:mb-4">Security Status</h2>
      
      <div class="space-y-3 sm:space-y-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            <span>Account Security</span>
          </div>
          <span class="badge badge-success">Secure</span>
        </div>
        
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" :class="userStore.profile ? 'text-success' : 'text-warning'" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            <span>Profile Complete</span>
          </div>
          <span class="badge" :class="userStore.isProfileComplete ? 'badge-success' : 'badge-warning'">
            {{ userStore.isProfileComplete ? 'Complete' : 'Incomplete' }}
          </span>
        </div>
        
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-warning" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            <span>Trigger Settings</span>
          </div>
          <span class="badge badge-warning">Review Needed</span>
        </div>
      </div>
      
      <div class="mt-3 sm:mt-4">
        <NuxtLink to="/dashboard/security" class="btn btn-xs sm:btn-sm btn-outline w-full">
          Review Security
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { useWillStore } from '~/stores/willStore';
import { useUserStore } from '~/stores/userStore';
import type { AuditLog } from '~/types';
import OnboardingWizard from '~/components/dashboard/OnboardingWizard.vue';

definePageMeta({
  layout: 'dashboard'
});

const user = useSupabaseUser();
const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();
const willStore = useWillStore();
const userStore = useUserStore();

// User name handling
const userName = computed(() => {
  if (userStore.profile?.firstName) {
    return userStore.profile.firstName;
  }
  if (user.value?.email) {
    return user.value.email.split('@')[0];
  }
  return 'there';
});

// Inline completion state for user-driven tasks
const checklistState = ref<{ [key: string]: boolean }>({});
const CHECKLIST_KEY = 'dashboard_checklist_state';

const loadChecklistState = () => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem(CHECKLIST_KEY);
    if (stored) checklistState.value = JSON.parse(stored);
  }
};
const saveChecklistState = () => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(CHECKLIST_KEY, JSON.stringify(checklistState.value));
  }
};

const showOnboarding = ref(false);

onMounted(() => {
  loadChecklistState();
  if (typeof window !== 'undefined' && !localStorage.getItem('onboarding_complete')) {
    showOnboarding.value = true;
  }
});

watch(checklistState, saveChecklistState, { deep: true });

// Dynamic Will Tasks
const willTasks = computed(() => [
  {
    id: 'will-1',
    text: 'Create a will document',
    completed: !!willStore.willDocument,
  },
  {
    id: 'will-2',
    text: 'Add personal information',
    completed: userStore.isProfileComplete,
  },
  {
    id: 'will-3',
    text: 'Add at least one article/section',
    completed: (willStore.willDocument?.content && willStore.willDocument.content.replace(/<[^>]*>/g, '').trim().length > 50) || false,
  },
  {
    id: 'will-4',
    text: 'Review legal requirements',
    completed: checklistState.value['will-4'] || false,
  },
  {
    id: 'will-5',
    text: 'Finalize and publish',
    completed: willStore.isPublished,
  },
]);

// Dynamic Asset Tasks
const assetTasks = computed(() => [
  {
    id: 'asset-1',
    text: 'Add at least one digital asset',
    completed: assetStore.digitalAssets.length > 0,
  },
  {
    id: 'asset-2',
    text: 'Add at least one financial asset',
    completed: assetStore.financialAssets.length > 0,
  },
  {
    id: 'asset-3',
    text: 'Add at least one physical asset',
    completed: assetStore.physicalAssets.length > 0,
  },
  {
    id: 'asset-4',
    text: 'Assign beneficiaries to all assets',
    completed: assetStore.assets.length > 0 && assetStore.assets.every(a => a.beneficiaries && a.beneficiaries.length > 0),
  },
]);

// Dynamic Beneficiary Tasks
const beneficiaryTasks = computed(() => [
  {
    id: 'ben-1',
    text: 'Add at least one primary beneficiary',
    completed: beneficiaryStore.primaryBeneficiaries.length > 0,
  },
  {
    id: 'ben-2',
    text: 'Assign at least one asset to a beneficiary',
    completed: beneficiaryStore.beneficiaries.some(b => b.assets && b.assets.length > 0),
  },
  {
    id: 'ben-3',
    text: 'Set access permissions for beneficiaries',
    completed: beneficiaryStore.beneficiaries.some(b => b.accessLevel !== 'LIMITED'),
  },
]);

// Completion calculations
const completionPercentage = computed(() => {
  const total = [...willTasks.value, ...assetTasks.value, ...beneficiaryTasks.value];
  if (total.length === 0) return 0;
  const completed = total.filter(t => t.completed).length;
  return Math.round((completed / total.length) * 100);
});

const willDocumentCompletion = computed(() => {
  return calculatePercentage(willTasks.value);
});

const assetCompletion = computed(() => {
  return calculatePercentage(assetTasks.value);
});

const beneficiaryCompletion = computed(() => {
  return calculatePercentage(beneficiaryTasks.value);
});

// Toggle task completion for user-driven tasks
const toggleTaskCompletion = (id: string) => {
  // Only allow toggling for user-driven tasks (e.g., review legal requirements)
  if (id === 'will-4') {
    checklistState.value[id] = !checklistState.value[id];
  }
};

// Recent activity (would be fetched from audit logs)
const recentActivity = ref<AuditLog[]>([]);

// Format date for display
const formatDate = (isoDate: string) => {
  const date = new Date(isoDate);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Fetch data on mount
onMounted(async () => {
  await Promise.all([
    userStore.fetchProfile(),
    assetStore.fetchAssets(),
    beneficiaryStore.fetchBeneficiaries(),
    willStore.fetchWillDocument()
  ]);
});

// Watch for changes and update task completion
watch([
  () => assetStore.assets.length,
  () => beneficiaryStore.beneficiaries.length,
  () => willStore.hasContent,
  () => userStore.isProfileComplete
], () => {
  // No need to update task completion manually as it's handled by the computed properties
});

// Calculate completion percentage for a list of tasks
function calculatePercentage(tasks: { completed: boolean }[]) {
  if (!tasks.length) return 0;
  const completed = tasks.filter(t => t.completed).length;
  return Math.round((completed / tasks.length) * 100);
}

function openOnboarding() {
  showOnboarding.value = true;
}
</script>