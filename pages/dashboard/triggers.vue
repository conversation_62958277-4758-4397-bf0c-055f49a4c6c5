<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Trigger Settings</h1>
        <p class="text-base-content/70">Configure how your will is executed</p>
      </div>
    </div>
    
    <div class="bg-base-100 shadow-md rounded-lg p-6 mb-8">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold">Execution Trigger</h2>
          <p class="text-base-content/70">Configure when your will should be executed</p>
        </div>
        
        <div class="form-control">
          <label class="label cursor-pointer">
            <span class="label-text mr-2">Active</span> 
            <input type="checkbox" v-model="triggerSettings.isActive" class="toggle toggle-primary" />
          </label>
        </div>
      </div>
      
      <div class="form-control mb-4">
        <label class="label">
          <span class="label-text">Inactivity Period</span>
        </label>
        <div class="flex items-center gap-2">
          <button class="btn btn-xs btn-outline" @click="triggerSettings.inactivityPeriod = Math.max(1, triggerSettings.inactivityPeriod - 1)">-</button>
          <input 
            type="number" 
            v-model.number="triggerSettings.inactivityPeriod"
            min="1" 
            max="120" 
            class="input input-xs w-20 text-center" 
          />
          <button class="btn btn-xs btn-outline" @click="triggerSettings.inactivityPeriod = Math.min(120, triggerSettings.inactivityPeriod + 1)">+</button>
          <span>day(s)</span>
        </div>
        <label class="label">
          <span class="label-text-alt">Process will begin after this period of inactivity</span>
        </label>
      </div>
      
      <div class="form-control mb-4">
        <label class="label">
          <span class="label-text">Reminder Frequency</span>
        </label>
        <div class="flex items-center gap-2">
          <button class="btn btn-xs btn-outline" @click="triggerSettings.reminderFrequency = Math.max(1, triggerSettings.reminderFrequency - 1)">-</button>
          <input 
            type="number" 
            v-model.number="triggerSettings.reminderFrequency"
            min="1" 
            max="90" 
            class="input input-xs w-20 text-center" 
          />
          <button class="btn btn-xs btn-outline" @click="triggerSettings.reminderFrequency = Math.min(90, triggerSettings.reminderFrequency + 1)">+</button>
          <span>day(s)</span>
        </div>
        <label class="label">
          <span class="label-text-alt">How often you'll receive activity reminders</span>
        </label>
      </div>
      
      <div class="form-control mb-6">
        <label class="label">
          <span class="label-text">Verification Method</span>
        </label>
        <select v-model="triggerSettings.verificationMethod" class="select select-bordered w-full">
          <option value="EMAIL">Email</option>
          <option value="SMS">SMS</option>
          <option value="SECURITY_QUESTIONS">Security Questions</option>
          <option value="TRUSTED_CONTACTS">Trusted Contacts</option>
        </select>
        <label class="label">
          <span class="label-text-alt">How we'll verify your status during the inactivity period</span>
        </label>
      </div>
      
      <div class="alert alert-info mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div>
          <span class="font-medium">How it works: </span>
          If we don't detect any activity from you for {{ triggerSettings.inactivityPeriod }} days, we\'ll begin the verification process. If you don't respond, your will execution process will begin.
        </div>
      </div>
    </div>
    
    <!-- Trusted Contacts Section -->
    <div class="bg-base-100 shadow-md rounded-lg p-6 mb-8">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold">Trusted Contacts</h2>
          <p class="text-base-content/70">People who can verify your status</p>
        </div>
        
        <button @click="showAddContactModal = true" class="btn btn-sm btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Add Contact
        </button>
      </div>
      
      <div class="overflow-x-auto">
        <table class="table w-full">
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Phone</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(contact, index) in trustedContacts" :key="contact.id">
              <td>
                <div class="flex items-center space-x-3">
                  <div class="avatar">
                    <div class="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center font-medium">
                      {{ getInitials(contact.name) }}
                    </div>
                  </div>
                  <div>
                    <div class="font-medium">{{ contact.name }}</div>
                    <div class="text-xs text-base-content/60">{{ contact.relationship || 'Contact' }}</div>
                  </div>
                </div>
              </td>
              <td>{{ contact.email }}</td>
              <td>{{ contact.phoneNumber || '-' }}</td>
              <td>
                <span class="badge" :class="contact.isVerified ? 'badge-success' : 'badge-warning'">
                  {{ contact.isVerified ? 'Verified' : 'Pending' }}
                </span>
              </td>
              <td>
                <div class="flex space-x-1">
                  <button 
                    v-if="!contact.isVerified"
                    @click="resendInvitation(contact.id)" 
                    class="btn btn-xs btn-ghost tooltip" 
                    data-tip="Resend Invitation"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </button>
                  <button 
                    @click="editContact(contact.id)" 
                    class="btn btn-xs btn-ghost tooltip" 
                    data-tip="Edit Contact"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </button>
                  <button 
                    @click="confirmDeleteContact(contact.id)" 
                    class="btn btn-xs btn-ghost tooltip" 
                    data-tip="Remove Contact"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
            
            <tr v-if="trustedContacts.length === 0">
              <td colspan="5" class="text-center py-4">
                <p class="text-base-content/70">No trusted contacts added yet</p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- Confirmation Steps -->
    <div class="bg-base-100 shadow-md rounded-lg p-6">
      <h2 class="text-xl font-semibold mb-4">Confirmation Steps</h2>
      <p class="text-base-content/70 mb-6">Configure the steps required before your will is executed</p>
      
      <div class="space-y-4">
        <div 
          v-for="(step, index) in confirmationSteps" 
          :key="index"
          class="flex justify-between items-center p-3 border border-base-300 rounded-lg"
        >
          <div class="flex items-center">
            <div class="bg-primary/10 text-primary w-8 h-8 rounded-full flex items-center justify-center font-bold mr-3">
              {{ index + 1 }}
            </div>
            <div>
              <h3 class="font-medium">{{ formatConfirmationType(step.type) }}</h3>
              <p class="text-xs text-base-content/60">{{ getStepDescription(step.type) }}</p>
            </div>
          </div>
          
          <div class="flex">
            <button @click="moveStep(index, -1)" class="btn btn-sm btn-ghost" :disabled="index === 0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
              </svg>
            </button>
            <button @click="moveStep(index, 1)" class="btn btn-sm btn-ghost" :disabled="index === confirmationSteps.length - 1">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </button>
            <button @click="removeStep(index)" class="btn btn-sm btn-ghost text-error">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Add Step Button -->
      <div class="mt-6">
        <div class="dropdown">
          <label tabindex="0" class="btn btn-outline w-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Confirmation Step
          </label>
          <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-72">
            <li v-for="(type, index) in availableStepTypes" :key="index">
              <a @click="addStep(type)">{{ formatConfirmationType(type) }}</a>
            </li>
          </ul>
        </div>
      </div>
      
      <!-- Save Button -->
      <div class="mt-8">
        <button @click="saveSettings" class="btn btn-primary w-full" :class="{ 'loading': isSaving }">
          {{ isSaving ? 'Saving...' : 'Save Trigger Settings' }}
        </button>
      </div>
    </div>
    
    <!-- Add Contact Modal -->
    <div class="modal" :class="{ 'modal-open': showAddContactModal }">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Add Trusted Contact</h3>
        
        <form @submit.prevent="saveContact" class="space-y-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Full Name</span>
            </label>
            <input 
              type="text" 
              v-model="newContact.name"
              class="input input-bordered w-full" 
              placeholder="John Doe"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Email</span>
            </label>
            <input 
              type="email" 
              v-model="newContact.email"
              class="input input-bordered w-full" 
              placeholder="<EMAIL>"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Phone Number</span>
            </label>
            <input 
              type="tel" 
              v-model="newContact.phoneNumber"
              class="input input-bordered w-full" 
              placeholder="+1234567890"
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Relationship</span>
            </label>
            <input 
              type="text" 
              v-model="newContact.relationship"
              class="input input-bordered w-full" 
              placeholder="Friend, Family Member, etc."
            />
          </div>
          
          <div class="modal-action">
            <button type="button" @click="showAddContactModal = false" class="btn">Cancel</button>
            <button type="submit" class="btn btn-primary" :disabled="!newContact.name || !newContact.email">
              Add Contact
            </button>
          </div>
        </form>
      </div>
      <div class="modal-backdrop" @click="showAddContactModal = false"></div>
    </div>
    
    <!-- Delete Contact Modal -->
    <div class="modal" :class="{ 'modal-open': showDeleteContactModal }">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Remove Trusted Contact</h3>
        <p>Are you sure you want to remove this trusted contact? They will no longer be able to verify your status.</p>
        <div class="modal-action">
          <button @click="showDeleteContactModal = false" class="btn">Cancel</button>
          <button @click="deleteContact" class="btn btn-error">Remove</button>
        </div>
      </div>
      <div class="modal-backdrop" @click="showDeleteContactModal = false"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { TrustedContact, ConfirmationType } from '~/types';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { useUserStore } from '~/stores/userStore';
import type { Beneficiary } from '~/types';

definePageMeta({
  layout: 'dashboard'
});

const beneficiaryStore = useBeneficiaryStore();
const userStore = useUserStore();

// Modals
const showAddContactModal = ref(false);
const showDeleteContactModal = ref(false);
const contactToDelete = ref<string | null>(null);

// Trigger settings
const triggerSettings = ref({
  id: 'trigger1',
  userId: 'user1',
  inactivityPeriod: 90, // days
  reminderFrequency: 30, // days
  verificationMethod: 'EMAIL',
  isActive: true,
  lastActivity: new Date().toISOString(),
  createdAt: '2023-05-01T10:00:00Z',
  updatedAt: '2023-05-01T10:00:00Z'
});

// Trusted contacts
const trustedContacts = ref<TrustedContact[]>([
  {
    id: 'contact1',
    userId: 'user1',
    name: 'Emily Johnson',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
    relationship: 'Sister',
    isVerified: true,
    createdAt: '2023-05-01T10:00:00Z',
    updatedAt: '2023-05-01T10:00:00Z'
  }
]);

// New contact form
const newContact = ref({
  name: '',
  email: '',
  phoneNumber: '',
  relationship: ''
});

// Confirmation steps
const confirmationSteps = ref([
  {
    id: 'step1',
    triggerId: 'trigger1',
    type: 'USER_RESPONSE' as ConfirmationType,
    isCompleted: false,
    createdAt: '2023-05-01T10:00:00Z',
    updatedAt: '2023-05-01T10:00:00Z'
  },
  {
    id: 'step2',
    triggerId: 'trigger1',
    type: 'CONTACT_VERIFICATION' as ConfirmationType,
    isCompleted: false,
    createdAt: '2023-05-01T10:00:00Z',
    updatedAt: '2023-05-01T10:00:00Z'
  },
  {
    id: 'step3',
    triggerId: 'trigger1',
    type: 'WAITING_PERIOD' as ConfirmationType,
    isCompleted: false,
    createdAt: '2023-05-01T10:00:00Z',
    updatedAt: '2023-05-01T10:00:00Z'
  }
]);

// Available step types
const availableStepTypes = ['USER_RESPONSE', 'CONTACT_VERIFICATION', 'SECURITY_QUESTION', 'WAITING_PERIOD'] as ConfirmationType[];

const isSaving = ref(false);

// Alert system state
const alerts = ref([
  { days: 30, recipients: [] as string[], custom: [] as { name: string; email: string }[] }
]);

const addAlert = () => {
  alerts.value.push({ days: 30, recipients: [], custom: [] });
};
const removeAlert = (idx: number) => {
  if (alerts.value.length > 1) alerts.value.splice(idx, 1);
};

const incrementDays = (alert: any) => {
  if (alert.days < 120) alert.days++;
};
const decrementDays = (alert: any) => {
  if (alert.days > 1) alert.days--;
};

const hasBeneficiaries = computed(() => beneficiaryStore.beneficiaries.length > 0);
const allBeneficiaries = computed(() => beneficiaryStore.beneficiaries);

const addCustomRecipient = (alert: any) => {
  alert.custom.push({ name: '', email: '' });
};
const removeCustomRecipient = (alert: any, idx: number) => {
  alert.custom.splice(idx, 1);
};

const saveAlerts = async () => {
  // Placeholder: Here you would call a Supabase Edge Function to save/send alerts
  alert('Alerts saved! (Email sending integration coming soon)');
};

// Helper functions
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase();
};

const formatConfirmationType = (type: ConfirmationType) => {
  const formatMap: Record<ConfirmationType, string> = {
    USER_RESPONSE: 'User Response Check',
    CONTACT_VERIFICATION: 'Trusted Contact Verification',
    SECURITY_QUESTION: 'Security Question Verification',
    WAITING_PERIOD: 'Final Waiting Period'
  };
  
  return formatMap[type] || type;
};

const getStepDescription = (type: ConfirmationType) => {
  const descriptionMap: Record<ConfirmationType, string> = {
    USER_RESPONSE: 'Send an email to check if you are still active',
    CONTACT_VERIFICATION: 'Ask trusted contacts to verify your status',
    SECURITY_QUESTION: 'Require answers to security questions',
    WAITING_PERIOD: 'Final waiting period before will execution'
  };
  
  return descriptionMap[type] || '';
};

// Actions
const moveStep = (index: number, direction: number) => {
  const newIndex = index + direction;
  if (newIndex < 0 || newIndex >= confirmationSteps.value.length) return;
  
  // Swap steps
  const steps = [...confirmationSteps.value];
  const temp = steps[index];
  steps[index] = steps[newIndex];
  steps[newIndex] = temp;
  
  confirmationSteps.value = steps;
};

const removeStep = (index: number) => {
  confirmationSteps.value = confirmationSteps.value.filter((_, i) => i !== index);
};

const addStep = (type: ConfirmationType) => {
  confirmationSteps.value.push({
    id: `step${Date.now()}`,
    triggerId: triggerSettings.value.id,
    type,
    isCompleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });
};

const saveContact = () => {
  // Add new contact
  trustedContacts.value.push({
    id: `contact${Date.now()}`,
    userId: 'user1',
    name: newContact.value.name,
    email: newContact.value.email,
    phoneNumber: newContact.value.phoneNumber || undefined,
    relationship: newContact.value.relationship || undefined,
    isVerified: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });
  
  // Reset form and close modal
  newContact.value = { name: '', email: '', phoneNumber: '', relationship: '' };
  showAddContactModal.value = false;
};

const editContact = (id: string) => {
  // In a real app, would show edit form
  alert(`Edit contact: ${id}`);
};

const confirmDeleteContact = (id: string) => {
  contactToDelete.value = id;
  showDeleteContactModal.value = true;
};

const deleteContact = () => {
  if (contactToDelete.value) {
    trustedContacts.value = trustedContacts.value.filter(c => c.id !== contactToDelete.value);
    showDeleteContactModal.value = false;
    contactToDelete.value = null;
  }
};

const resendInvitation = (id: string) => {
  // In a real app, would resend invitation
  alert(`Invitation resent to contact: ${id}`);
};

const saveSettings = async () => {
  isSaving.value = true;
  
  try {
    // Simulate saving to Supabase
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    triggerSettings.value.updatedAt = new Date().toISOString();
    
    // Show success notification
    alert('Trigger settings saved successfully!');
  } finally {
    isSaving.value = false;
  }
};
</script>