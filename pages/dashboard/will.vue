<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Will Document</h1>
        <p class="text-base-content/70">Create and edit your comprehensive will document</p>
      </div>
      
      <div class="flex space-x-2">
        <button @click="showTemplateModal = true" class="btn btn-sm btn-outline">
          <Icon icon="mdi:file-document-multiple" class="h-4 w-4 mr-1" />
          Use Template
        </button>
        
        <button @click="saveDocument" class="btn btn-sm btn-primary" :class="{ 'loading': willStore.isSaving }">
          <Icon v-if="!willStore.isSaving" icon="mdi:content-save" class="h-4 w-4 mr-1" />
          {{ willStore.isSaving ? 'Saving...' : 'Save' }}
        </button>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="willStore.isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <!-- Will Document Form -->
    <div v-else class="bg-base-100 shadow-md rounded-lg p-4 md:p-6 mb-6">
      <div class="form-control mb-4">
        <label class="label">
          <span class="label-text font-medium">Document Title</span>
        </label>
        <input 
          type="text" 
          v-model="documentTitle"
          @blur="saveTitle"
          class="input input-bordered w-full" 
          placeholder="My Digital Will & Testament"
        />
      </div>
      
      <div class="form-control mb-4">
        <label class="label">
          <span class="label-text font-medium">Will Content</span>
          <span class="label-text-alt">{{ formatLastSaved() }}</span>
        </label>
        <EnhancedWillEditor 
          v-model="documentContent" 
          placeholder="Begin writing your will document here. Use the Insert menu to add assets, beneficiaries, and instructions..."
        />
      </div>
      
      <!-- Document Stats -->
      <div class="stats stats-horizontal shadow mt-4">
        <div class="stat">
          <div class="stat-title">Word Count</div>
          <div class="stat-value text-primary">{{ willStore.wordCount }}</div>
        </div>
        
        <div class="stat">
          <div class="stat-title">Status</div>
          <div class="stat-value text-sm">
            <span class="badge" :class="willStore.isPublished ? 'badge-success' : 'badge-warning'">
              {{ willStore.isPublished ? 'Published' : 'Draft' }}
            </span>
          </div>
        </div>
        
        <div class="stat">
          <div class="stat-title">Last Updated</div>
          <div class="stat-value text-sm">{{ formatDate(willStore.willDocument?.updatedAt) }}</div>
        </div>
      </div>
    </div>
    
    <!-- AI Assistant Panel -->
    <div class="bg-base-100 shadow-md rounded-lg p-4 md:p-6 mb-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="font-semibold text-lg flex items-center">
          <Icon icon="mdi:robot" class="h-5 w-5 mr-2 text-primary" />
          AI Assistant
        </h3>
        
        <button @click="toggleAIAssistant" class="btn btn-sm btn-ghost">
          <Icon :icon="showAIAssistant ? 'mdi:chevron-up' : 'mdi:chevron-down'" class="h-5 w-5" />
        </button>
      </div>
      
      <div v-if="showAIAssistant" class="animate-fade-in">
        <div class="mb-4">
          <p class="text-sm mb-3">Ask the AI assistant to help you with your will:</p>
          <div class="flex">
            <input 
              type="text" 
              v-model="aiPrompt"
              class="input input-bordered flex-1 mr-2" 
              placeholder="E.g., Help me write a section about my digital assets"
              @keyup.enter="generateAIResponse"
            />
            <button @click="generateAIResponse" class="btn btn-primary" :class="{ 'loading': isGenerating }">
              <Icon v-if="!isGenerating" icon="mdi:lightning-bolt" class="h-5 w-5" />
            </button>
          </div>
        </div>
        
        <div class="divider text-xs text-base-content/50">Quick Actions</div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
          <button 
            v-for="(template, index) in aiTemplates" 
            :key="index"
            @click="applyTemplate(template.content)"
            class="btn btn-sm btn-outline btn-block justify-start text-left"
          >
            {{ template.name }}
          </button>
        </div>
        
        <div v-if="aiResponse" class="mt-4 p-4 bg-primary/5 border border-primary/20 rounded-lg">
          <div class="flex justify-between items-start mb-2">
            <p class="text-sm font-semibold text-primary">AI Suggestion</p>
            <div class="flex space-x-1">
              <button @click="applyAIResponse" class="btn btn-xs btn-primary tooltip" data-tip="Apply suggestion">
                <Icon icon="mdi:check" class="h-3 w-3" />
              </button>
              <button @click="aiResponse = ''" class="btn btn-xs btn-ghost tooltip" data-tip="Dismiss">
                <Icon icon="mdi:close" class="h-3 w-3" />
              </button>
            </div>
          </div>
          <div class="text-sm prose prose-sm max-w-none" v-html="aiResponse"></div>
        </div>
      </div>
    </div>
    
    <!-- Legal Requirements Check -->
    <div class="bg-base-100 shadow-md rounded-lg p-4 md:p-6">
      <h3 class="font-semibold text-lg mb-4 flex items-center">
        <Icon icon="mdi:shield-check" class="h-5 w-5 mr-2 text-accent" />
        Legal Requirements Check
      </h3>
      
      <div class="space-y-4">
        <div class="flex items-start">
          <div class="min-w-[24px] mt-0.5">
            <Icon icon="mdi:check-circle" class="h-6 w-6 text-success" />
          </div>
          <div class="ml-3">
            <p class="font-medium">Personal Information</p>
            <p class="text-sm text-base-content/70">Your full legal name and personal details are included.</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="min-w-[24px] mt-0.5">
            <Icon
              :icon="assetStore.assets.length > 0 ? 'mdi:check-circle' : 'mdi:alert-triangle'"
              class="h-6 w-6"
              :class="assetStore.assets.length > 0 ? 'text-success' : 'text-warning'"
            />
          </div>
          <div class="ml-3">
            <p class="font-medium">Asset Documentation</p>
            <p class="text-sm text-base-content/70">
              {{ assetStore.assets.length > 0 
                ? `${assetStore.assets.length} assets documented` 
                : 'No assets documented yet' }}
            </p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="min-w-[24px] mt-0.5">
            <Icon
              :icon="beneficiaryStore.beneficiaries.length > 0 ? 'mdi:check-circle' : 'mdi:alert-triangle'"
              class="h-6 w-6"
              :class="beneficiaryStore.beneficiaries.length > 0 ? 'text-success' : 'text-warning'"
            />
          </div>
          <div class="ml-3">
            <p class="font-medium">Beneficiaries</p>
            <p class="text-sm text-base-content/70">
              {{ beneficiaryStore.beneficiaries.length > 0 
                ? `${beneficiaryStore.beneficiaries.length} beneficiaries added` 
                : 'No beneficiaries added yet' }}
            </p>
          </div>
        </div>
      </div>
      
      <div class="mt-6 p-4 bg-info/10 rounded-lg">
        <p class="text-sm">
          <span class="font-medium">Note:</span> 
          Laws regarding digital wills vary by jurisdiction. This check is provided as guidance only and does not constitute legal advice. Consider consulting with an estate attorney in your area.
        </p>
      </div>
    </div>
    
    <!-- Template Modal -->
    <div class="modal" :class="{ 'modal-open': showTemplateModal }">
      <div class="modal-box max-w-2xl">
        <h3 class="font-bold text-lg mb-4">Select a Template</h3>
        
        <div class="space-y-4">
          <div v-for="(template, index) in templates" :key="index" 
               class="p-4 border border-base-300 rounded-lg hover:bg-base-200 cursor-pointer transition-colors"
               @click="() => { useTemplate(template.content); showTemplateModal = false; }">
            <h4 class="font-medium text-lg mb-2">{{ template.name }}</h4>
            <p class="text-sm text-base-content/70 mb-3">{{ template.description }}</p>
            <div class="flex flex-wrap gap-1">
              <span v-for="feature in template.features" :key="feature" class="badge badge-outline badge-sm">
                {{ feature }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="modal-action">
          <button @click="showTemplateModal = false" class="btn">Cancel</button>
        </div>
      </div>
      <div class="modal-backdrop" @click="showTemplateModal = false"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { Icon } from '@iconify/vue';
import { useWillStore } from '~/stores/willStore';
import { useAssetStore } from '~/stores/assetStore';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import EnhancedWillEditor from '~/components/editor/EnhancedWillEditor.vue';
import CreateAssetModal from '~/components/editor/CreateAssetModal.vue';
import CreateBeneficiaryModal from '~/components/editor/CreateBeneficiaryModal.vue';

definePageMeta({
  layout: 'dashboard'
});

const willStore = useWillStore();
const assetStore = useAssetStore();
const beneficiaryStore = useBeneficiaryStore();

// Will document data
const documentTitle = ref('');
const documentContent = ref('');

// AI Assistant
const showAIAssistant = ref(true);
const aiPrompt = ref('');
const aiResponse = ref('');
const isGenerating = ref(false);

// Template modal
const showTemplateModal = ref(false);

// Watch for changes in will document
watch(() => willStore.willDocument, (newDoc) => {
  if (newDoc) {
    documentTitle.value = newDoc.title;
    documentContent.value = newDoc.content;
  }
}, { immediate: true });

const toggleAIAssistant = () => {
  showAIAssistant.value = !showAIAssistant.value;
};

const generateAIResponse = async () => {
  if (!aiPrompt.value) return;
  
  isGenerating.value = true;
  
  try {
    // Simulate AI response generation
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Mock AI response based on prompt
    if (aiPrompt.value.toLowerCase().includes('digital assets')) {
      aiResponse.value = `
        <h3>Digital Assets Section</h3>
        <p>I designate the following digital assets to be distributed as specified:</p>
        <ul>
          <li>Cryptocurrency wallets and associated private keys</li>
          <li>Online accounts including email, social media, and cloud storage</li>
          <li>Digital files, photos, and documents</li>
          <li>Domain names and intellectual property</li>
        </ul>
        <p>Access information for these assets is stored securely and can be accessed by my digital executor using the procedures outlined in the attached instructions.</p>
      `;
    } else if (aiPrompt.value.toLowerCase().includes('executor')) {
      aiResponse.value = `
        <h3>Executor Appointment</h3>
        <p>I hereby appoint [Full Name] of [Address], as the Executor of this Will. If they are unable or unwilling to act as Executor, I appoint [Alternative Full Name] of [Address] to be the Executor.</p>
        <p>I grant my Executor full power and authority to:</p>
        <ul>
          <li>Access and manage all digital accounts and assets</li>
          <li>Distribute assets according to my wishes</li>
          <li>Handle all legal and administrative matters</li>
        </ul>
      `;
    } else if (aiPrompt.value.toLowerCase().includes('beneficiar')) {
      aiResponse.value = `
        <h3>Beneficiary Designations</h3>
        <p>I bequeath my assets to the following beneficiaries:</p>
        <ul>
          <li>[Beneficiary Name] - [Specific assets or percentage]</li>
          <li>[Beneficiary Name] - [Specific assets or percentage]</li>
        </ul>
        <p>If any named beneficiary predeceases me, their share shall be distributed to [specify contingent arrangements].</p>
      `;
    } else {
      aiResponse.value = `
        <p>Based on your query, I recommend adding a specific section that clearly outlines your wishes regarding the matters you mentioned. Would you like me to help you create a template for this specific topic?</p>
        <p>Consider including:</p>
        <ul>
          <li>Clear identification of assets or beneficiaries</li>
          <li>Specific instructions for access or distribution</li>
          <li>Contingency plans for various scenarios</li>
        </ul>
      `;
    }
    
    aiPrompt.value = '';
  } finally {
    isGenerating.value = false;
  }
};

const applyAIResponse = () => {
  documentContent.value += aiResponse.value;
  aiResponse.value = '';
};

// Document templates
const templates = [
  {
    name: 'Basic Digital Will',
    description: 'A simple template covering digital assets and basic distributions',
    features: ['Digital Assets', 'Basic Executor', 'Simple Distribution'],
    content: `
<h1>Last Will and Testament</h1>
<p>I, [FULL LEGAL NAME], a resident of [CITY, STATE], being of sound mind, declare this to be my Last Will and Testament.</p>

<h2>Article I - Revocation</h2>
<p>I hereby revoke all former wills and codicils previously made by me.</p>

<h2>Article II - Digital Executor</h2>
<p>I appoint [NAME] as my Digital Executor to manage and distribute my digital assets.</p>

<h2>Article III - Digital Assets</h2>
<p>I own the following digital assets, which shall be distributed as indicated:</p>
<p>[Use the Insert menu to add your specific assets]</p>

<h2>Article IV - Access Information</h2>
<p>Access information for my digital assets is stored securely and can be accessed by my Digital Executor using the procedure outlined in my Digital Asset Instructions document.</p>
    `
  },
  {
    name: 'Comprehensive Will',
    description: 'Detailed template covering both digital and physical assets',
    features: ['Digital Assets', 'Physical Assets', 'Multiple Executors', 'Detailed Instructions'],
    content: `
<h1>Last Will and Testament</h1>
<p>I, [FULL LEGAL NAME], a resident of [CITY, STATE], being of sound mind, declare this to be my Last Will and Testament.</p>

<h2>Article I - Revocation</h2>
<p>I hereby revoke all former wills and codicils previously made by me.</p>

<h2>Article II - Executor Appointment</h2>
<p>I appoint [NAME] as the Executor of my estate. If they are unable or unwilling to serve, I appoint [ALTERNATE NAME] as alternate Executor.</p>

<h2>Article III - Digital Executor</h2>
<p>I appoint [NAME] as my Digital Executor to manage and distribute my digital assets.</p>

<h2>Article IV - Digital Assets</h2>
<p>I own the following digital assets, which shall be distributed as indicated:</p>
<p>[Use the Insert menu to add your specific assets and beneficiaries]</p>

<h2>Article V - Physical Assets</h2>
<p>I bequeath my physical assets as follows:</p>
<p>[List physical assets and beneficiaries]</p>

<h2>Article VI - Residuary Estate</h2>
<p>I give my residuary estate to [NAME/NAMES].</p>

<h2>Article VII - Digital Asset Access</h2>
<p>Access information for my digital assets is stored securely and can be accessed by my Digital Executor using the procedure outlined in my Digital Asset Instructions document.</p>
    `
  },
  {
    name: 'Cryptocurrency Focus',
    description: 'Template with detailed sections for crypto wallets and keys',
    features: ['Cryptocurrency', 'Security Protocols', 'Multi-step Verification', 'Recovery Plans'],
    content: `
<h1>Last Will and Testament</h1>
<p>I, [FULL LEGAL NAME], a resident of [CITY, STATE], being of sound mind, declare this to be my Last Will and Testament.</p>

<h2>Article I - Revocation</h2>
<p>I hereby revoke all former wills and codicils previously made by me.</p>

<h2>Article II - Cryptocurrency Executor</h2>
<p>I appoint [NAME] as my Cryptocurrency Executor to manage and distribute my cryptocurrency assets.</p>

<h2>Article III - Cryptocurrency Assets</h2>
<p>I own the following cryptocurrency assets, which shall be distributed as indicated:</p>
<p>[Use the Insert menu to add your cryptocurrency assets]</p>

<h2>Article IV - Cryptocurrency Access</h2>
<p>Access information, including wallet addresses, seed phrases, and private keys are stored securely using [METHOD]. My Cryptocurrency Executor can access this information by [SECURE ACCESS PROCESS].</p>

<h2>Article V - Security Protocol</h2>
<p>To access my cryptocurrency holdings, the following multi-step verification process must be followed:</p>
<p>[Use the Insert menu to add detailed security instructions]</p>

<h2>Article VI - Contingency Plan</h2>
<p>If my primary Cryptocurrency Executor is unable or unwilling to perform these duties, I appoint [ALTERNATE NAME] as alternate Cryptocurrency Executor.</p>
    `
  }
];

// AI templates
const aiTemplates = [
  { name: 'Add Digital Asset Section', content: '<h2>Digital Assets Distribution</h2><p>I own the following digital assets, which shall be distributed as follows:</p><ul><li>Email accounts: [List email addresses]</li><li>Social media accounts: [List social media accounts]</li><li>Cloud storage: [List cloud storage accounts]</li><li>Subscription services: [List subscription services]</li><li>Domain names: [List domain names]</li><li>Cryptocurrency: [List cryptocurrency wallets]</li></ul><p>Access information for these accounts is stored in my password manager, which my digital executor can access using the instructions provided separately.</p>' },
  { name: 'Add Executor Section', content: '<h2>Appointment of Executor</h2><p>I hereby appoint [EXECUTOR NAME] of [EXECUTOR ADDRESS] as the executor of this will and of my estate. If [EXECUTOR NAME] is unable or unwilling to act as executor, I appoint [ALTERNATE EXECUTOR NAME] of [ALTERNATE EXECUTOR ADDRESS] to be the executor.</p><p>I direct my executor to pay all my debts, funeral expenses, testamentary expenses, and taxes as soon as practical.</p>' },
  { name: 'Add Beneficiary Section', content: '<h2>Beneficiary Designations</h2><p>I bequeath my assets to the following beneficiaries:</p><ul><li>[BENEFICIARY NAME] - [ASSET/PERCENTAGE]</li><li>[BENEFICIARY NAME] - [ASSET/PERCENTAGE]</li><li>[BENEFICIARY NAME] - [ASSET/PERCENTAGE]</li></ul><p>If any of the named beneficiaries predeceases me, their share shall be distributed [TO THEIR DESCENDANTS PER STIRPES / TO THE REMAINING BENEFICIARIES IN EQUAL SHARES].</p>' },
  { name: 'Add Instructions Block', content: '<p>[Click here to add detailed instructions for accessing your digital assets]</p>' }
];

// Save functions
const saveTitle = async () => {
  await willStore.saveWillDocument(documentTitle.value);
};

const saveDocument = async () => {
  await willStore.saveWillDocument(documentTitle.value, documentContent.value);
};

// Auto-save when content changes
let autoSaveTimeout: NodeJS.Timeout | null = null;
watch([documentContent], () => {
  if (autoSaveTimeout) {
    clearTimeout(autoSaveTimeout);
  }
  
  autoSaveTimeout = setTimeout(() => {
    saveDocument();
  }, 3000);
});

// Apply template
const useTemplate = (content: string) => {
  // Ask for confirmation if document already has content
  if (documentContent.value && documentContent.value.trim() !== '') {
    if (confirm('This will replace your current document content. Are you sure?')) {
      documentContent.value = content;
    }
  } else {
    documentContent.value = content;
  }
};

// Apply AI template
const applyTemplate = (content: string) => {
  documentContent.value += content;
};

// Format functions
const formatLastSaved = () => {
  if (!willStore.lastSaved) return 'Not saved yet';
  return `Last saved: ${new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(willStore.lastSaved)}`;
};

const formatDate = (dateString?: string) => {
  if (!dateString) return 'Never';
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString));
};

// Fetch data on mount
onMounted(async () => {
  await willStore.fetchWillDocument();
  await assetStore.fetchAssets();
  await beneficiaryStore.fetchBeneficiaries();
});

onBeforeUnmount(() => {
  if (autoSaveTimeout) {
    clearTimeout(autoSaveTimeout);
  }
});
</script>
